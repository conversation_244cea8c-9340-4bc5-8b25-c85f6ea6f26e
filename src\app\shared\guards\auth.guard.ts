import { inject } from '@angular/core';
import { Router } from '@angular/router';
import { CanActivateFn } from '@angular/router';
import { AuthService } from '@shared/auth/auth.service';

export const authGuard: CanActivateFn = (route, state) => {
	const authService = inject(AuthService);
	const router = inject(Router);

	if (authService.isLoggedIn()) {
		return true;
	}

	// Redirect to login page with return url
	router.navigate(['/login'], { queryParams: { returnUrl: state.url } });
	return false;
};
