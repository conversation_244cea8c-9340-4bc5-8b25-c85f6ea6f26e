.orll-residents-detail {
	&__panel {
		border: 3px solid var(--iata-grey-50);
		border-radius: 8px;

		margin: 30px auto;
		display: block;
	}

	&__panel .mat-expansion-panel {
		background-color: var(--iata-white) !important;
		margin: 0;
	}

	.delete-button {
		margin-left: auto !important;
	}

	.form-strech {
		flex: 1;
	}

	.mat-expansion-panel {
		outline: none;
		border-left: none;
	}

	.mat-expansion-panel:focus {
		outline: none;
	}
}

.permissions-container {
	display: flex;
	flex-wrap: wrap;
	gap: 16px;
	align-items: center;
}

.permission-item {
	min-width: 200px;
	margin: 4px 0;
}

.d-flex {
	margin-top: 20px;
}

.Warn-label {
	color: red;
	margin-left: 20px;
}
