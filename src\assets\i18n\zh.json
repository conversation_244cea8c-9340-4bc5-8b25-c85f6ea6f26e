{"validators": {"required": "{{field}} is required", "email": "Please enter a valid email address", "number": "Please enter number", "pattern": "Please enter a valid {{requiredPattern}} value in {{field}}", "minlength": "Please enter at least {{minLength}} characters", "maxlength": "Please enter no more than {{maxLength}} characters", "min": "Please enter a value greater than or equal to {{min}}", "max": "Please enter a value less than or equal to {{max}}", "minDate": "Please enter a value greater than or equal to {{minDate}}", "maxOneDecimal": "{{field}} is positive number with maximum 1 decimal", "maxTwoDecimal": "{{field}} is positive number with maximum 2 decimal", "maxDecimal1": "positive number with maximum 1 decimal", "maxDecimal2": "positive number with maximum 2 decimal", "maxOneDecimalRound5": "{{field}} is positive number with maximum 1 decimal and round up to 0.5", "positiveNumber": "{{field}} is positive number"}, "common": {"mainHeader.app.title": "ONE Record", "mgmt.shipment": "SLI", "mainHeader.app.subtitle": "创新应用实验室", "mainHeader.mainNav.shipment": "货物管理", "mainHeader.mainNav.sli": "托运人货物委托书", "mainHeader.mainNav.sli.piece": "单件列表", "mainHeader.mainNav.hawb": "HAWB", "mainHeader.mainNav.mawb": "MAWB", "mainHeader.mainNav.changeRequests": "变更请求", "mainHeader.mainNav.mawb.fhl": "分舱单", "mainHeader.mainNav.upload": "Upload", "mainHeader.mainNav.payment": "Payment", "mainHeader.mainNav.fileBrowser": "File Browser", "mainHeader.mainNav.checkin": "Check-in", "mainHeader.mainNav.mf": "MF", "mainHeader.mainNav.users": "用户", "mainHeader.mainNav.subscription": "订阅", "mainHeader.mainNav.distribution": "询价订舱", "mainHeader.mainNav.distribution.quote": "询价", "mainHeader.mainNav.distribution.booking": "订舱", "mainHeader.language.english": "英文", "mainHeader.language.chinese": "中文", "mainHeader.settings": "Settings", "mainHeader.account": "Account", "mainHeader.userid": "User ID", "mainHeader.currentRole": "Current Role", "mainHeader.resources": "Resources", "mainHeader.logout": "注销", "mainHeader.mainNav.partner": "合作伙伴", "mainHeader.mainNav.partner.permission": "权限", "mainHeader.mainNav.partner.subscription": "订阅", "mainHeader.mainNav.system": "系统设置", "mainHeader.userlogin.success": "登录成功", "mainHeader.userlogin.fail": "登录失败", "mainFooter.title": "Our mission is to represent,", "mainFooter.subtitle": "lead and serve the airline industry", "mainFooter.support": "Support", "mainFooter.services": "Services", "mainFooter.store": "IATA store", "mainFooter.privacy": "Privacy", "mainFooter.legal": "Legal", "breadcrumb.home": "Home", "dialog.next": "下一步", "dialog.cancel": "取消", "dialog.later": "稍后", "dialog.ok": "确定", "dialog.revoke": "撤销", "dialog.approve": "批准", "dialog.reject": "拒绝", "dialog.alert.title": "注意", "dialog.alert.content.piece": "No permission granted on Pieces", "dialog.alert.content.403": "没有权限", "dialog.request.delegation": "申请权限", "dialog.delegation.request.title": "访问授权请求", "dialog.delegation.request.description": "描述", "dialog.delegation.request.for": "授权给", "dialog.delegation.request.by": "请求发起人", "dialog.delegation.request.at": "请求时间", "dialog.delegation.request.status": "请求状态", "dialog.delegation.request.permissions": "权限", "dialog.delegation.request.logisticsObject": "物流对象", "dialog.delegation.request.create": "发起权限请求", "dialog.delegation.request.send": "发送", "dialog.delegation.request.approve": "批准", "dialog.delegation.request.reject": "拒绝", "delegation.permission.GET_LOGISTICS_EVENT": "获取物流事件", "delegation.permission.GET_LOGISTICS_OBJECT": "获取物流对象", "delegation.permission.POST_LOGISTICS_EVENT": "创建物流事件", "delegation.permission.PATCH_LOGISTICS_OBJECT": "更新物流对象", "delegation.request.tab": "权限请求", "dialog.cancel.content": "Are you sure to cancel?", "dialog.delete.content": "Are you sure to delete?", "dialog.unConsolidate.conent": "Are you sure to break down?", "selectOrg.title": "Select One from Below", "confirm.title": "确认", "dialog.form.validate": "Please fill out all the required and valid data.", "dialog.orglist.fail": "Get organization list failed", "dialog.orginfo.fail": "Get organization info failed", "change.request": {"object.type": "物流对象类型", "object.url": "物流对象URI", "version": "Version", "date": "日期", "changed.by": "变更人", "status": "状态", "title": "变更历史", "request.number": "主运单编号", "dialog.title": "变更请求", "request.by": "请求发起人", "description": "描述您的变更请求", "btn.revoke": "撤销", "btn.reject": "拒绝", "btn.approve": "批准", "detail.table.obj": "物流对象", "detail.table.property": "变更字段", "detail.table.oldValue": "初始值", "detail.table.newValue": "新值", "no.permission": "No permission to retrieve version info"}, "table.action": "", "copy.success": "URI is copied", "copy.failed": "URI copy failed", "copy.no.text": "No text to be copied", "retrieve.btn": "获取", "enter.uri.btn": "输入URI获取", "enter.uri.title": "输入URI获取", "no.access.error": "Not authorized to perform action error then it can follow the Request Access Delegation flow to request access", "skip.btn": "跳过"}, "sli": {"mgmt.title": "单件管理", "mgmt.goodsDescription": "货物描述", "mgmt.sliCode": "货物托运书编号", "mgmt.shipper": "Shipper Company Name", "mgmt.consignee": "Consignee Company Name", "mgmt.departureLocation": "始发站", "mgmt.arrivalLocation": "目的站", "mgmt.hawbNumber": "关联分运单", "mgmt.createDate": "Created Date Range", "mgmt.search": "搜索", "mgmt.reset": "重置", "mgmt.create": "创建货物托运书", "mgmt.createHawb": "创建", "mgmt.edit": "编辑货物托运书", "mgmt.cancel": "取消", "mgmt.save": "保存", "mgmt.add.piece": "单件", "mgmt.delete.piece": "删除", "mgmt.list": "单件列表", "mgmt.list.total.quantity": "单件总数", "mgmt.list.total.slac": "SLAC总数", "mgmt.company": {"companyName": "公司名称", "contactName": "联系人姓名", "country": "国家", "province": "省份", "city": "城市名称", "textualPostCode": "邮政编码", "address": "地址", "phoneNumber": "电话号码", "emailAddress": "邮箱", "shipper": "托运人", "consignee": "收货人", "alsoNotify": "其他通知方", "companyName.required": "Company Name is required", "contactName.required": "Contact Name is required", "country.required": "Country is required", "province.required": "Province is required", "city.required": "City is required", "address.required": "Address is required", "phoneNumber.required": "Phone Number is required", "pattern": {"number": "Please enter number", "email": "Please enter a valid email address"}}, "mgmt.routing": {"departureLocation": "始发站", "arrivalLocation": "目的站", "departureLocation.required": "Airport of Departure is required", "arrivalLocation.required": "Airport of Destination is required", "shippingInfo": "财务信息"}, "mgmt.pieceList": {"goodsDescription": "货物描述", "totalGrossWeight": "总毛重", "totalDimensions": "尺寸", "dimLength": "长", "dimWidth": "宽", "dimHeight": "高", "goodsDescription.required": "Description of Goods is required", "totalGrossWeight.required": "Total Gross Weight is required", "dimLength.required": "Length is required", "dimWidth.required": "Width is required", "dimHeight.required": "Height is required", "declaredValueForCustoms": "供海关用声明价值", "declaredValueForCarriage": "供运输用声明价值", "insuredAmount": "保险价值", "textualHandlingInstructions": "操作注意事项", "weightValuationIndicator": "运费/声明价值附加费预付/到付", "incoterms": "Incoterms", "pattern": {"positiveNumber": "positive number", "decimalNumber1": "positive number with maximum 1 decimal", "decimalNumber2": "positive number with maximum 2 decimals", "decimalNumber2NIL": "positive number with maximum 2 decimals or NIL", "decimalNumber2NVD": "positive number with maximum 2 decimals or NVD", "decimalNumber2NCV": "positive number with maximum 2 decimals or NCV"}, "create.step1": "第一步:创建货物"}, "piece": {"table.column.productDescription": "产品描述", "table.column.packagingType": "包装类型", "table.column.grossWeight": "毛重(公斤)", "table.column.dimensions": "尺寸", "table.column.pieceQuantity": "件数", "table.column.slac": "SLAC", "table.column.actions": "操作", "table.column.latestStatus": "最新事件", "addDialog.title": "选择单件类型", "addDialog.subtitle": "请选择要添加的单件类型", "addDialog.general": "普通货物", "addDialog.dg": "危险品", "addDialog.la": "活体动物", "addDialog.pieceType.required": "Piece type is required", "grossWeight.required": "Gross Weight is required", "packagingType.required": "Packaging Type is required", "productDescription.required": "Product Description is required", "add": "添加单件", "edit": "编辑单件", "add.pieceIn": "添加", "title": "单件", "productDescription": "产品描述", "grossWeight": "毛重", "dimensions": "尺寸", "upid": "件级货物编号", "packages": "包装", "packagingType": "包装类型", "packagedIdentifier": "包装件识别码", "hsCommodityDescription": "HS商品描述", "nvdForCustoms": "是否有海关用声明价值", "nvdForCarriage": "是否有运输用声明价值", "shippingMarks": "运输标记", "textualHandlingInstructions": "其它操作要求", "pieceQuantity": "件数", "pieceQuantity.required": "Piece Quantity is required", "done": "完成", "item.title": "Do you want to create Contained Pieces in this Piece?", "item.add": "添加内含小件", "contained.yes": "是", "contained.no": "否", "contained.title": "Contained Pieces", "item.description": "内含小件描述", "item.weight": "重量", "item.quantity": "数量", "item.total": "内含小件总数", "slac.total": "SLAC", "create.success": "Create piece successfully", "create.fail": "Create piece failed", "update.success": "Update piece successfully", "update.fail": "Update piece failed", "detail.fail": "Get piece detail failed", "list.fail": "Get piece list failed", "no.sli.fail": "Please create SLI first--", "consolidate.title": "集中托运单件", "consolidate.btn": "集中托运"}, "dgPiece": {"title": "危险品单件", "formItem": {"productDescription": "产品描述", "typeOfPackage": "包装类型", "packagedIdentifier": "包装件识别码", "whetherHaveDeclaredValueForCustoms": "是否有海关用声明价值", "whetherHaveDeclaredValueForCarriage": "是否有运输用声明价值", "specialProvisionId": "特殊规定编号", "explosiveCompatibilityGroupCode": "爆炸品配装组代码", "packagingDangerLevelCode": "包装危险等级代码", "technicalName": "技术名称", "unNumber": "UN编号", "shippersDeclaration": "托运人危险品申报", "handlingInformation": "其它操作要求", "allPackedInOne": "不同危险品装入同一外包装", "qValueNumeric": "Q值", "upid": "件级货物编号", "shippingMarks": "运输标记", "grossWeight": "毛重", "dimensions": "尺寸", "hsCommodityDescription": "HS商品描述", "properShippingName": "运输专用名称", "textualHandlingInstructions": " ", "hazardClassificationId": "危险类别编号", "additionalHazardClassificationId": "次要危险类别编号", "packingInstructionNumber": "包装说明编号", "complianceDeclaration": "符合性声明", "exclusiveUseIndicator": "专用标识", "authorizationInformation": "授权信息", "aircraftLimitationInformation": "机型限制信息", "item.productDescription": "产品描述", "item.quantity": "数量", "item.weight": "重量", "item.title": "小件", "item.add": "添加"}}, "liveAnimalPiece": {"title": "活体动物单件", "formItem": {"productDescription": "产品描述", "typeOfPackage": "包装类型", "packagedIdentifier": "包装件识别码", "speciesCommonName": "物种通用名", "speciesScientificName": "物种学名", "specimenDescription": "物种描述", "animalQuantity": "动物数量", "shippingMarks": "运输标记", "upid": "件级货物编号", "grossWeight": "毛重", "dimensions": "尺寸", "whetherHaveDeclaredValueForCustoms": "是否有海关用声明价值", "whetherHaveDeclaredValueForCarriage": "是否有运输用声明价值", "textualHandlingInstructions": " "}}, "table.column.sliCode": "货物托运书编号", "table.column.shipper": "托运人", "table.column.consignee": "收货人", "table.column.goodsDescription": "货物描述", "table.column.departureLocation": "始发站", "table.column.arrivalLocation": "目的站", "table.column.slac": "单件数", "table.column.createDate": "创建日期", "table.column.receivedFrom": "接收来源", "table.column.hawbNumber": "关联分运单", "table.column.share": "分享", "dialog.create.success": "Create SLI successfully", "dialog.create.fail": "Create SLI failed", "dialog.update.success": "Update SLI successfully", "dialog.update.fail": "Update SLI failed", "dialog.detail.fail": "Get SLI detail failed", "dialog.list.fail": "Get SLI list failed", "dialog.shipper.fail": "Get shipper info failed", "share.title": "分享货物托运书", "create.pieces.confirm": "Please finalize SLI creation later to associate these pieces"}, "shared": {"table.column.name": "公司名称", "table.column.orgType": "公司类型", "button.name": "分享"}, "hawb": {"mgmt.title": "分运单", "mgmt.hawbNumber": "分运单编号", "mgmt.create": "根据SLI创建分运单", "mgmt.create.fromSli": "根据货物托运书创建分运单", "mgmt.create.fromSliDetail": "创建分运单", "mgmt.edit": "编辑分运单", "mgmt.fhl.total": "分运单总数", "mgmt.fhl.total.slac": "SLAC总数", "mgmt.fhl.total.piece": "单件总数", "mgmt.piecelist": "分运单单件清单", "mgmt.total.piece": "单件总数", "mgmt.associated.sli": "关联货物托运书", "mgmt.latest.status": "最新事件", "table.column.hawbNumber": "分运单编号", "table.column.shipper": "托运人", "table.column.consignee": "收货人", "table.column.goodsDescription": "货物描述", "table.column.origin": "始发站", "table.column.destination": "目的站", "table.column.pieceQuantity": "单件数", "table.column.createDate": "创建日期", "table.column.latestStatus": "最新事件", "table.column.eventDate": "事件日期", "table.column.mawbNumber": "关联主运单", "table.column.share": "分享", "table.column.copy": "复制", "table.column.totalGrossWeight": "重量", "table.column.pieceNumber": "单件数", "table.column.slac": "SLAC", "table.column.textualHandlingInformation": "Special Handing Code", "table.column.countryCode": "Custom Information ISO Country Code", "table.column.contentCode": "Customs, Security and Regulatory Control Information Identifier", "table.column.otherCustomsInformation": "Supplementary Customs, Security and Regulatory Control                 Information", "dialog.list.fail": "Get HAWB list failed", "createHawb.success": "Create HAWB Success", "preview.awb": "预览运单", "updateHawb.success": "HAWB is successfully updated", "updateHawb.error": "HAWB update fails due to server unavailability, please try again", "share.title": "分享分运单", "carrierAgent": {"title": "承运人代理", "company": "公司名称", "agentIataCode": "代理IATA代码", "country": "国家", "province": "省份", "cityName": "城市名称", "textualPostCode": "邮政编码", "address": "地址", "phoneNumber": "电话号码", "email": "邮箱", "formInvalid": "Carrier's Agent Form Invalid"}, "issuedBy": {"title": "发布人员姓名", "content": "To be populated when MAWB is created"}, "formItem": {"hawbPrefix": "分运单前缀", "hawbNumber": "分运单编号", "accountingInformation": "财务信息", "handingInformation": "操作注意事项", "noOfPiecesRcp": "实际接收件数(件数/组合运价点)", "grossWeight": "毛重", "volume": "体积", "rateClass": "费率等级", "chargeableWeight": "计费重量", "rateCharge": "费率/费用", "total": "总额", "natureAndQuantityOfGoods": "货物品名与数量（包括尺寸或体积）", "date": "日期", "atPlace": "签署地点", "signatureOfShipperOrHisAgent": "发托运人或其代理人签字", "signatureOfCarrierOrItsAgent": "承运人或其代理人签字"}, "airportInfo": {"departureAndRequestedRouting": "起运机场及要求路线", "departureAndRequestedRouting.required": "Airport of Departure and Requested Routing is required", "airportOfDestination": "目的站", "amountOfInsurance": "保险金额", "flight": "航班", "to": "目的地", "toBy2ndCarrier": "第二承运人目的地", "toBy3rdCarrier": "第三承运人目的地", "date": "日期", "byFirstCarrier": "第一承运人", "by2ndCarrier": "第二承运人", "by3rdCarrier": "第三承运人", "wtOrVal": "运费/声明价值附加费", "other": "其他费用", "declaredValueForCarriage": "供运输用声明价值", "declaredValueForCustoms": "供海关用声明价值"}, "prepaidAndCollect": {"prepaidTitle": "预付", "collectTitle": "到付", "weightCharge": "运费", "valuationCharge": "声明价值附加费", "tax": "税费", "totalOtherChargesDueAgent": "应付代理人其他费用总额", "totalOtherChargesDueCarrier": "应付承运人其他费用总额", "totalPrepaid": "预付总额", "totalCollect": "到付总额"}, "otherCharges": {"title": "其他费用", "chargePaymentType": "付款方式", "entitlement": "费用归属控制码", "otherChargeCode": "其他费用代码", "otherChargeAmount": "其他费用金额", "addButton": "其他费用", "formInvalid": "Carrier's Agent Form Invalid"}}, "mawb": {"mgmt.title": "主运单", "mgmt.mawbNumber": "主运单编号", "mgmt.create": "从分运单创建主运单", "mgmt.create.fromHawb": "从我的分运单创建主运单", "mgmt.create.fromHawbDetail": "主运单", "mgmt.create.fromSelectedHawb": "选择以创建主运单", "mgmt.edit": "编辑主运单", "mgmt.export": "导出PDF", "mgmt.close": "关闭", "mgmt.exporting": "导出中", "exportpdf.success": "Export PDF successfully", "exportpdf.failed": "Export PDF failed", "exportpdf.generating": "Generating PDF...", "mgmt.airlineCode": "航空公司代码", "mgmt.latestStatus": "最新事件", "mgmt.btn.post.request": "创建变更请求", "mgmt.accountingNoteText.shipper": "<PERSON><PERSON>'s Account Number", "mgmt.accountingNoteText.consignee": "Consignee's Account Number", "mgmt.shipment.shipper": "新的托运人", "mgmt.shipment.consignee": "新的收货人", "table.column.share": "分享", "table.column.copy": "复制", "table.column.mawbNumber": "主运单编号", "table.column.airlineCode": "航空公司代码", "table.column.goodsDescription": "货物描述", "table.column.origin": "始发站", "table.column.destination": "目的站", "table.column.latestStatus": "最新事件", "table.column.eventDate": "事件日期", "table.column.createDate": "创建日期", "share.title": "分享主运单", "createMawb.success": "成功创建主运单", "preview.awb": "预览运单", "updateMawb.success": "MAWB is successfully updated", "post.success": "Post Change Request has been successfully sent", "updateMawb.error": "MAWB update fails due to server unavailability, please try again", "dialog.weight.validate": "selected HAWBs have different WT/VAL value", "dialog.other.validate": "selected HAWBs have different Other charge type value", "carrierAgent": {"title": "承运人代理", "company": "公司名称", "agentIataCode": "代理IATA代码", "accountingNoteText": "Account No.", "country": "国家", "province": "省份", "cityName": "城市名称", "textualPostCode": "邮政编码", "address": "地址", "phoneNumber": "电话号码", "email": "邮箱", "formInvalid": "Carrier's Agent Form Invalid"}, "issuedBy": {"title": "发布人员姓名", "content": "To be populated when MAWB is created"}, "formItem": {"mawbPrefix": "主运单前缀", "mawbNumber": "主运单编号", "mawbNumber.checkLength": "MAWB Number must be 8 digits", "mawbNumber.checkDigit": "MAWB Number's 8th digit should be the mod7 of the first 7 digits, which is ", "accountingInformation": "财务信息", "handingInformation": "操作注意事项", "noOfPiecesRcp": "实际接收件数(件数/组合运价点)", "grossWeight": "毛重", "volume": "体积", "serviceCode": "服务代码", "rateClass": "费率等级", "chargeableWeight": "计费重量", "rateCharge": "费率/费用", "total": "总额", "natureAndQuantityOfGoods": "货物品名与数量（包括尺寸或体积）", "destinationCurrencyRate": "货币兑换率", "destinationCollectCharges": "到付费用按目的地货币收取", "totalCollectCharges": "到付费用总额", "destinationCharges": "目的地费用", "shippingInfo": "可选托运人信息", "shippingRefNo": "参考编号", "date": "日期", "atPlace": "签署地点", "signatureOfShipperOrHisAgent": "发托运人或其代理人签字", "signatureOfCarrierOrItsAgent": "承运人或其代理人签字"}, "airportInfo": {"departureAndRequestedRouting": "起运机场及要求路线", "airportOfDestination": "目的站", "amountOfInsurance": "保险金额", "chargesCode": "费用代码", "flight": "航班", "to": "目的地", "toBy2ndCarrier": "第二承运人目的地", "toBy3rdCarrier": "第三承运人目的地", "date": "日期", "byFirstCarrier": "第一承运人", "by2ndCarrier": "第二承运人", "by3rdCarrier": "第三承运人", "wtOrVal": "运费/声明价值附加费", "other": "其他费用", "declaredValueForCarriage": "供运输用声明价值", "declaredValueForCustoms": "供海关用声明价值"}, "prepaidAndCollect": {"prepaidTitle": "预付", "collectTitle": "到付", "weightCharge": "运费", "valuationCharge": "声明价值附加费", "tax": "税费", "totalOtherChargesDueAgent": "应付代理人其他费用总额", "totalOtherChargesDueCarrier": "应付承运人其他费用总额", "totalPrepaid": "预付总额", "totalCollect": "到付总额"}, "otherCharges": {"title": "其他费用", "chargePaymentType": "付款方式", "entitlement": "费用归属控制码", "otherChargeCode": "其他费用代码", "otherChargeAmount": "其他费用金额", "addButton": "其他费用", "formInvalid": "Carrier's Agent Form Invalid"}, "event": {"update.title": "事件更新", "milestone.date": "注明里程碑是否隔日发生", "planned.milestone": "此为计划里程碑", "partial.milestone": "此为部分达成里程碑", "update.btn": "更新", "bulk.update.btn": "批量更新", "update.log.btn": "更新日志", "update.log.title": "Log Items", "update.log.operationStatus": "操作状态", "update.log.loId": "物流对象编号", "update.log.type": "类型", "update.log.newValue": "里程碑", "update.log.errorMsg": "错误信息", "update.log.createDate": "创建日期", "update.log.userName": "更新操作人", "update.log.orgName": "Org Name", "time.type.error": "empty event time type", "update.time.error": "empty update time", "empty.error": "Please select the event", "choose.mawb": "Please select MAWB/HAWB/PIECE", "tracking.title": "事件跟踪", "status.history": "变更历史", "history.table.event": "事件", "history.table.time": "更新时间", "history.table.user": "更新操作人", "history.table.event.time.type": "事件时间类型", "history.table.partial.event": "部分事件", "no.permission": "No permission to retrieve Logistic Events", "back.btn": "返回"}}, "partner": {"mgmt.head": "Partner Access", "mgmt.title": "请注意以下规则已在系统中预制：", "mgmt.title1": "1.航空公司只能访问自己的主运单 （全部权限）", "mgmt.title2": "2.托运人只能访问和自己相关的分运单（全部权限）", "mgmt.mawbNumber": "主运单编号", "mgmt.addPartner": "添加合作伙伴", "mgmt.edit": "编辑", "mgmt.save": "保存", "mgmt.latestStatus": "最新事件", "table.column.businessData": "业务数据", "table.column.partner": "合作伙伴", "table.column.GET_LOGISTICS_OBJECT": "获取物流对象", "table.column.PATCH_LOGISTICS_OBJECT": "更新物流对象", "table.column.POST_LOGISTICS_EVENT": "创建物流事件", "table.column.GET_LOGISTICS_EVENT": "获取物流事件", "tab.configuration": "配置", "tab.request": "请求"}, "upload": {"browser.dragAndDrop": "Drag & Drop file(s) here", "browser.or": "或", "browser.browse": "Browse for file(s)", "progress.lastModified": "Last modified at:", "progress.fileSize": "File size:"}, "filesManagement": {"category": "File category", "table.column.name": "Name", "table.column.size": "Size", "table.column.createdAt": "Created at", "table.column.actions": "操作", "table.action.view": "查看", "table.action.download": "下载", "table.action.delete": "删除", "noFilesFound": "Sorry, we didn't find any files. You might need to upload them first!", "selectedFile.header": "Selected file view"}, "ifgPayment": {"paymentInitFailed": "Payment initiation failed!", "renderFailed": "Payment screen rendering failed!"}, "checkin": {"passenger": {"title": "Passenger details", "name": "姓名", "gender": "性别", "nationality": "国籍", "nationality.placeholder": "选择国籍", "birthDate": "出生日期"}, "flight": {"title": "航班信息", "carrier": "运营承运人", "carrier.placeholder": "选择一家航空公司", "departureAirport": "始发站", "departureAirport.placeholder": "选择机场", "departureDate": "出发日期", "arrivalAirport": "目的站", "arrivalAirport.placeholder": "选择机场", "arrivalDate": "到达日期", "flightNumber": "航班号", "addLeg": "Add Leg", "deleteLeg": "Delete Leg", "checkin": "Check-in"}, "boardingPass": {"title": "Here is your boarding pass!", "subTitle": "Send boarding pass to your e-mail address:", "email": "邮箱", "send": "发送"}}, "users": {"mgmt.list": "User Management", "mgmt.keyword": "Please input keyword to search", "mgmt.search": "搜索", "table.column.userName": "User Name", "table.column.email": "用户电子邮箱", "table.column.firstName": "名字", "table.column.lastName": "姓氏", "table.column.orgName": "公司 / 组织", "table.column.primaryOrgName": "主要所属公司", "table.column.userType": "用户类型", "table.column.roles": "角色", "table.column.status": "状态", "table.column.lastAccessed": "Last Accessed At", "table.column.actions": "操作", "table.action.updateUser": "更新用户", "buttons.create": "添加用户", "noDataFound": "We didn't find any existing user!", "mgmt.create.title": "创建用户", "mgmt.create.firstName": "名字", "mgmt.create.firstName.required": "First Name is required", "mgmt.create.lastName": "姓氏", "mgmt.create.lastName.required": "Last Name is required", "mgmt.create.email": "邮箱", "mgmt.create.email.required": "Email is required", "mgmt.create.orgName": "公司 / 组织", "mgmt.create.orgName.required": "Company/Organization is required", "mgmt.create.primaryOrgName": "主要所属公司", "mgmt.create.userType": "用户类型", "mgmt.create.userType.required": "User Type are required", "mgmt.create.primaryOrgName.required": "Primary Resident Company is required", "mgmt.create.secondary": "次要所属公司", "mgmt.create.secondary.orgName": "内部公司", "mgmt.create.secondary.add": "添加", "mgmt.create.email.iataEmail": "Email should be @iata.org or @external.iata.org", "create.email.duplicatedUser": "User with such email already exists", "create.roles": "角色", "create.roles.required": "Roles are required", "create.status": "状态", "create.status.required": "Status is required", "create.cancel": "取消", "create.submit": "保存", "update.title": "Update User", "update.submit": "保存"}, "pagination": {"itemsPerPage": "每页展示数量", "nextPage": "Next page", "previousPage": "Previous page", "firstPage": "First page", "lastPage": "Last page", "rangeEmpty": "0 of 0", "rangeLabel": "{{start}} - {{end}} of {{length}}"}, "subscription": {"title": "订阅", "booking.requset.text": "航空公司不能订阅货运代理人发给别的航空公司的订舱请求", "request.table.subscriber": "订阅方", "request.table.topicType": "主题类型", "request.table.topic": "主题", "request.table.requestBy": "请求发起人", "request.table.requestAt": "请求时间", "request.table.status": "请求状态", "request.detail": "请求详情", "detail.permissions": "权限", "tab.configuration": "配置", "tab.request": "请求", "config.table.eventType": "Subscription Event Type", "config.table.expiersAt": "到期时间", "btn.new": "New Subscription", "btn.invite": "邀请订阅", "create.title": "创建订阅", "edit.title": "编辑订阅", "form.event": "Subscription Event Type", "btn.invite.confirm": "邀请", "topic.placeholder": "input URI in here..."}, "notifications": {"title": "通知", "view.more": "Read all Notifications", "toggle.read": "仅未读", "btn.mark": "全部标记为已读", "share.content": " 已与您分享 ", "updated": " 已更新 ", "created": " 已创建 ", "event.content": " 已添加事件"}, "system": {"title": "系统管理", "rule": {"title": "规则", "table.holder": "Holder", "table.request.type": "请求类型", "table.requester": "请求方", "table.action": "操作", "info": "若主运单（MAWB）已标记RCS事件，则货运代理人将自动拒绝针对主运单的变更申请。", "approve": "自动批准", "reject": "自动拒绝", "create.title": "创建规则", "edit.title": "编辑规则", "requester.error1": "holders are more than one, requesters must be empty", "requester.error2": "requester can not include holders", "booking.info": "航空公司发布的报价是针对货运代理人的询价请求的变更请求。这种请求将被自动批准，以确保货运代理人能及时查看航司的报价。"}, "server": {"title": "Server", "matpanel.title.server": "服务器", "matpanel.label.uri": "URI", "matpanel.label.endpoint": "端点", "matpanel.label.apiVersion": "API版本", "matpanel.label.contentType": "内容类型", "matpanel.label.encoding": "编码", "matpanel.label.language": "语言", "matpanel.label.ontology": "本体", "matpanel.label.ontologyVersion": "本体版本", "matpanel.title.organization": "组织", "matpanel.label.residentsType": "Residents Type", "matpanel.label.companyName": "公司名称", "matpanel.label.forwarderIATACode": "货代IATA代码", "matpanel.label.airlineCode": "航空公司代码", "matpanel.label.airlinePrefix": "航空公司前缀", "matpanel.label.country": "国家", "matpanel.label.province": "省份", "matpanel.label.city": "城市名称", "matpanel.label.address": "地址", "matpanel.label.postCode": "邮政编码", "matpanel.title.keycloak": "Keycloak", "matpanel.label.graphDbUrl": "GraphDb Url", "matpanel.label.neOneUrl": "NeOne Url", "matpanel.label.keycloakUrl": "Keycloak Url", "warn": "Usually you should NOT change Keycloak settings. Do you confirm to change?", "matpanel.label.grantType": "GrantType", "matpanel.label.clientId": "Client Id", "matpanel.label.clientSecret": "Client Secret", "matpanel.label.logisticsAgentUri": "Logistics Agent <PERSON><PERSON>", "matpanel.title.contact": "联系人", "matpanel.label.contactRole": "联系人角色", "matpanel.label.jobTitle": "职位头衔", "matpanel.label.contactName": "联系人姓名", "matpanel.label.contactDetailType": "联系方式", "matpanel.label.textualValue": " ", "button.retrieveServerInfo": "获取服务器信息", "button.retrieveOrganizationInfo": "获取组织信息", "button.retrieveContactInfo": "获取联系人信息", "button.save": "保存", "button.cancel": "取消", "button.addNewExternalServer": "添加新的外部服务器", "button.deleteExternalServer": "删除外部服务器", "tab.oRLLResident": "ORLL内部公司", "tab.externalServers": "外部服务器", "contact.new": "添加联系人", "retrive.org.btn": "获取组织信息", "retrive.contact.btn": "获取联系人信息", "uri": "URI", "matpanel.title.permission": "菜单权限"}}, "booking": {"option": {"table.product": "请求的产品", "table.departure": "请求始发站", "table.arrival": "请求目的站", "table.status": "状态", "create.btn": "发起询价", "view.title": "询价请求", "chargeable.weight": "计费重量", "commodity.code": "Commodity Code", "handling.code": "Special Handling Codes--", "handling.instruction": "其它操作要求", "subTitle.itinerary": "行程", "departure.location": "始发站", "arrival.location": "目的站", "max.segment": "最大航段数", "transport.id": "航班偏好", "earliest.acceptance.time": "最早接受时间", "latest.acceptance.time": "最晚接受时间", "latest.arrival.time": "最晚到达时间", "shipment.available.date": "Shipment Available Date", "currency": "货币", "subTitle.preference": "偏好", "subTitle.booking.shipment": "订舱货物", "select.airline": "选择一家航空公司", "request.title": "Booking Option Request from ", "to.forwarder": "发回给货代", "request.booking": "请求订舱", "from": " - IATA代码 #", "form.departure": "始发站", "form.arrival": "目的站", "form.airline.code": "运营承运人（航空公司代码）", "form.flight.number": "航班号", "form.departure.time": "出发日期 / 时间", "form.arrival.time": "到达日期 / 时间", "form.charge.type": "费用类型", "form.charge.rate.class": "费率等级", "form.rate": "费率 / 按重量计费", "form.payment.type": "支付类型", "form.entitlement": "费用归属控制码", "form.product.code": "产品代码", "form.offer.valid.from": "报价有效期从", "form.offer.valid.to": "报价有效期至", "add.charge.btn": "Add Charge", "del.itinerary": "删除行程", "add.itinerary": "添加行程", "send.back.btn": "发回给货代", "add.booking.option.btn": "添加报价", "departure.check.warning": "Departure location should match previous arrival location", "price.title": "价格", "grand.total": "总计 =", "share.history": "询价历史", "request.to": "请求对象", "request.date": "请求日期", "created.succ": "Raise booking option request for mawb successfully", "created.failed": "Raise booking option request for mawb failed", "select": "请选择一个报价", "iata.code": "Code  "}, "mgmt.requestedBy": "请求发起人", "mgmt.requestedProduct": "请求的产品", "mgmt.requestedFlight": "请求的航班", "mgmt.requestedStatus": "Requested Status", "mgmt.departureLocation": "始发站", "mgmt.arrivalLocation": "目的站", "mgmt.flightDate": "航班日期", "mgmt.mawbNumber": "关联主运单", "request.pieceGroupCount": "单件数", "request.totalGrossWeight": "毛重", "request.chargeableWeight": "计费重量", "request.totalDimensions": "尺寸", "request.expectedCommodity": "Commodity Code", "request.specialHandlingCodes": "Special Handling Codes", "request.textualHandlingInstructions": "其它操作要求", "request.totalPrice": "价格总计", "dialog.title.option": "报价", "dialog.title.request": "订舱请求", "dialog.button.request": "请求订舱", "dialog.button.confirm": "确认订舱", "dialog.button.create.master": "创建主运单", "dialog.button.update.master": "更新主运单", "dialog.button.mawb": "创建主运单"}, "ecsd": {"title": "电子货物安保声明", "create.btn": "创建电子货物安保声明", "edit.btn": "编辑电子货物安保声明", "view.btn": "查看电子货物安保声明", "table.status": "电子货物安保状态", "table.method": "安检方式", "table.ground": "免于安检原因", "table.from": "接收来源", "table.issue.by": "发布人员姓名", "table.issue.on": "发布时间", "hawb.table.number": "分运单编号", "hawb.table.shipper": "托运人", "hawb.table.consignee": "收货人", "hawb.table.description": "货物描述", "hawb.table.origin": "始发站", "hawb.table.destination": "目的站", "hawb.table.weight": "重量", "hawb.table.slac": "SLAC", "info.regulated": "管制实体类别和标识符（发布安保状态的管制方）", "info.reason": "发布电子货物安保状态理由", "info.accepted": "管制实体类别和标识符（已接受另一管制代理人为托运货物提供的安保状态的管制方）", "form.category": "管制实体类别", "form.identifier": "管制实体标识符", "form.status": "电子货物安保状态", "form.screen": "是否免于安检", "form.method": "安检方式", "form.ground": "免于安检原因", "form.from": "接收来源", "form.issue.by": "发布人员姓名/雇员ID", "form.issue.on": "发布时间", "form.information": "附加安保信息", "table.piece.number": "单件编号", "table.hawb.number": "分运单编号", "table.mawb.number": "主运单编号", "table.air.code": "航空公司代码", "table.good.description": "货物描述", "table.package.type": "包装类型", "table.gross.weight": "毛重", "table.dimensions": "尺寸", "yes": "是", "no": "否", "save.btn": "保存"}, "calculate": {"title": "计算体积和计费重量", "column.productDescription": "产品描述", "column.dimensions": "尺寸", "column.grossWeight": "毛重", "column.slac": "slac", "label.pieceRCP": "实际接收件数(件数/组合运价点):", "label.pieceGrossWeight": "单件毛重:", "label.totalSlac": "SLAC总数:", "label.totalGrossWeight": "总毛重", "label.volume": "体积", "label.chargeableWeight": "计费重量", "btn.save": "保存", "btn.cancel": "取消"}, "home": {"dashboard": "系统看板", "dashboard.title": "欢迎来到ONE Record Living Lab，这是一个用于探索国际航空运输协会（IATA）ONE Record 功能的演示环境", "dashboard.links": "实用链接", "dashboard.links.standard.title": "ONE Record Standard Introduction", "dashboard.links.standard": "Introduction to ONE Record, a standard for data sharing and creates a single record view of the shipment", "dashboard.links.api.title": "ONE Record API Specification", "dashboard.links.api": "This ONE Record API specification is part of the ONE Record standard. It defines a standard, programming language-agnostic interface for the interaction with the ONE Record Web API.", "dashboard.links.dataModel.title": "ONE Record Data Model Specification", "dashboard.links.dataModel": "This ONE Record data model specification is part of the ONE Record standard. It details core concepts of the data model described in the ONE Record cargo and ONE Record core code lists ontologies. It aims to provide explanations and examples for usage of the data model for implementers.", "dashboard.chart.weeklyTotal": "新增用户总数/周", "dashboard.chart.weeklyExternal": "外部新增用户/周", "dashboard.chart.weeklyActive": "周活跃用户", "dashboard.chart.weeklyTotalUser": "注册用户总数/周", "dashboard.chart.weeklyTotalExternalUser": "外部用户总数/周", "dashboard.chart.weeklyTotalActiveUser": "周活跃用户总数", "dashboard.chart.userNumber": "用户数", "dashboard.chart.growthNumber": "增长数量", "dashboard.chart.growthRate": "增长率", "dashboard.activity.stream": "活动日志", "dashboard.activity.stream.clean": "Clean Up Activity Stream", "dashboard.activity.system.clean": "Clean Up System Data", "dashboard.activity.stream.clean.msg": "Are you sure to clean up all the Activity Stream logs? system data such as Shipment records(SLI/HAWB/MAWB) are not impacted.", "dashboard.activity.system.clean.msg": "Are you sure to clean up all the system data such as Shipment records(SLI/HAWB/MAWB), Change/Subscription/Access Delegation Requests, Subscriptions, Quote/Bookings and Notifications?", "dashboard.activity.clean": "清理", "dashboard.activity.stream.download": "下载JSON文件"}}