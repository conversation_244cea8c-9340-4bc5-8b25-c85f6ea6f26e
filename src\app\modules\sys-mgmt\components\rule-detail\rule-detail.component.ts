import { Component, Inject, OnInit } from '@angular/core';
import { RuleService } from '../../services/rule.service';
import { MAT_DIALOG_DATA, MatDialog, MatDialogModule, MatDialogRef } from '@angular/material/dialog';
import { MatInputModule } from '@angular/material/input';
import { MatDividerModule } from '@angular/material/divider';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { FormControl, FormGroup, ReactiveFormsModule, Validators } from '@angular/forms';
import { Organization } from '@shared/models/organization.model';
import { OrgMgmtRequestService } from '@shared/services/org-mgmt-request.service';
import { MatIconModule } from '@angular/material/icon';
import { MatButtonModule } from '@angular/material/button';
import { MatRadioModule } from '@angular/material/radio';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatSelectModule } from '@angular/material/select';
import { RuleDetailObj, RuleListObj } from '../../models/rule.model';
import { ConfirmDialogComponent } from '@shared/components/confirm-dialog/confirm-dialog.component';
import { RolesAwareComponent } from '@shared/components/roles-aware/roles-aware.component';

@Component({
	selector: 'orll-rule-detail',
	imports: [
		MatDialogModule,
		MatInputModule,
		MatDividerModule,
		TranslateModule,
		MatIconModule,
		MatButtonModule,
		MatRadioModule,
		MatFormFieldModule,
		ReactiveFormsModule,
		MatSelectModule,
	],
	templateUrl: './rule-detail.component.html',
	styleUrl: './rule-detail.component.scss',
})
export class RuleDetailComponent extends RolesAwareComponent implements OnInit {
	requestTypes = [
		{ code: '1', name: 'Change Request' },
		{ code: '2', name: 'Subscription Request' },
		{ code: '3', name: 'Access Delegation Request' },
	];
	ruleForm: FormGroup = new FormGroup({
		requestType: new FormControl<string>('', [Validators.required]),
		request: new FormControl<string[]>([], []),
		action: new FormControl<string>('', [Validators.required]),
	});

	isEdit = false;
	isSave = false;
	dataLoading = false;
	requestList: Organization[] = [];
	holderId = '';

	constructor(
		private readonly ruleService: RuleService,
		private readonly orgService: OrgMgmtRequestService,
		private readonly dialog: MatDialog,
		private readonly translateService: TranslateService,
		private readonly dialogRef: MatDialogRef<RuleDetailComponent>,
		@Inject(MAT_DIALOG_DATA) public data: RuleListObj
	) {
		super();
	}

	ngOnInit() {
		this.getCurrentUser().subscribe((res) => {
			this.holderId = res?.orgId ?? '';
		});

		this.orgService.getOrgList().subscribe((res) => {
			this.requestList = res;
		});

		if (this.data?.id) {
			this.dataLoading = true;
			this.ruleService.getRule(this.data).subscribe({
				next: (res) => {
					this.ruleForm.patchValue(res);
					this.isEdit = true;
					this.dataLoading = false;
				},
				error: () => (this.dataLoading = false),
			});
		}
	}

	getFormData(): RuleDetailObj {
		const formData: RuleDetailObj = {
			holder: this.holderId ? [this.holderId] : [],
			requestType: this.ruleForm.value.requestType ?? '',
			request: this.ruleForm.value.request ?? [],
			action: this.ruleForm.value.action ?? '',
		};
		if (this.data?.id) {
			formData.id = this.data.id;
		}
		return formData;
	}

	saveRule() {
		this.isSave = true;
		this.ruleForm.markAllAsTouched();
		if (this.ruleForm.invalid) {
			this.dialog.open(ConfirmDialogComponent, {
				width: '300px',
				autoFocus: false,
				data: {
					content: this.translateService.instant('common.dialog.form.validate'),
				},
			});
			return;
		}

		this.dataLoading = true;
		this.ruleService.saveRule(this.getFormData()).subscribe({
			next: () => {
				this.dataLoading = false;
				this.dialogRef.close(true);
			},
			error: () => {
				this.dataLoading = false;
			},
		});
	}
}
