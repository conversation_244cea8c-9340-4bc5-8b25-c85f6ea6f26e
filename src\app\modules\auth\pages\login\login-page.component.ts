import { Component, inject, OnInit } from '@angular/core';
import { FormControl, FormGroup, ReactiveFormsModule, Validators } from '@angular/forms';
import { Router, ActivatedRoute } from '@angular/router';
import { CommonModule } from '@angular/common';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatCardModule } from '@angular/material/card';
import { TranslateModule } from '@ngx-translate/core';
import { AuthService } from '@shared/auth/auth.service';
import { NotificationService } from '@shared/services/notification.service';
import { finalize } from 'rxjs/operators';

@Component({
	selector: 'iata-login-page',
	templateUrl: './login-page.component.html',
	styleUrls: ['./login-page.component.scss'],
	imports: [
		CommonModule,
		ReactiveFormsModule,
		MatFormFieldModule,
		MatInputModule,
		MatButtonModule,
		MatIconModule,
		MatCardModule,
		TranslateModule,
	],
})
export default class LoginPageComponent implements OnInit {
	private readonly authService = inject(AuthService);
	private readonly router = inject(Router);
	private readonly route = inject(ActivatedRoute);
	private readonly notificationService = inject(NotificationService);

	hidePassword = true;
	isLoading = false;
	returnUrl = '/';

	loginForm = new FormGroup({
		email: new FormControl('', [Validators.required, Validators.email]),
		password: new FormControl('', [Validators.required, Validators.minLength(6)]),
	});

	ngOnInit(): void {
		// Check if user is already logged in
		if (this.authService.isLoggedIn()) {
			this.router.navigate([this.returnUrl]);
			return;
		}

		// Get return url from route parameters or default to '/'
		this.returnUrl = this.route.snapshot.queryParams['returnUrl'] || '/';
	}

	onSubmit(): void {
		if (this.loginForm.valid) {
			this.isLoading = true;
			const { email } = this.loginForm.value;

			// For now, we'll use the existing login structure
			// In a real implementation, you'd send email/password to your auth endpoint
			const loginParams = {
				userId: email,
				orgId: 'ddbab271-1ddd-4cc4-b546-af9a0c0d4943', // This should come from your backend
			};

			this.authService
				.login(loginParams)
				.pipe(finalize(() => (this.isLoading = false)))
				.subscribe({
					next: () => {
						this.notificationService.showSuccess('auth.login.success');
						this.router.navigate([this.returnUrl]);
					},
					error: (error) => {
						console.error('Login failed:', error);
						this.notificationService.showError('auth.login.failed');
					},
				});
		} else {
			this.loginForm.markAllAsTouched();
		}
	}

	togglePasswordVisibility(): void {
		this.hidePassword = !this.hidePassword;
	}

	getEmailErrorMessage(): string {
		const emailControl = this.loginForm.get('email');
		if (emailControl?.hasError('required')) {
			return 'auth.login.email.required';
		}
		if (emailControl?.hasError('email')) {
			return 'validators.email';
		}
		return '';
	}

	getPasswordErrorMessage(): string {
		const passwordControl = this.loginForm.get('password');
		if (passwordControl?.hasError('required')) {
			return 'auth.login.password.required';
		}
		if (passwordControl?.hasError('minlength')) {
			return 'auth.login.password.minlength';
		}
		return '';
	}
}
