import { ChangeDetectionStrategy, ChangeDetectorRef, Component, Inject, OnInit } from '@angular/core';
import { MatDialogModule, MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';
import { MatInputModule } from '@angular/material/input';
import { CommonModule } from '@angular/common';
import { MatIconModule } from '@angular/material/icon';
import { RolesAwareComponent } from '@shared/components/roles-aware/roles-aware.component';
import { TranslateModule } from '@ngx-translate/core';
import { SpinnerComponent } from '@shared/components/spinner/spinner.component';
import { MatListModule } from '@angular/material/list';
import { MatButtonModule } from '@angular/material/button';
import { BookingTransportComponent } from '../booking-transport/booking-transport.component';
import { BookingRequestService } from '../../services/booking-request.service';
import { BookingRequestDetailObj } from '@shared/models/booking-option.model';
import { Router } from '@angular/router';
import { UserRole } from '@shared/models/user-role.model';

@Component({
	selector: 'orll-booking-request-details',
	imports: [
		CommonModule,
		MatIconModule,
		MatButtonModule,
		TranslateModule,
		SpinnerComponent,
		MatInputModule,
		MatListModule,
		MatDialogModule,
		BookingTransportComponent,
	],
	templateUrl: './booking-request-details.component.html',
	styleUrl: './booking-request-details.component.scss',
	changeDetection: ChangeDetectionStrategy.OnPush,
})
export class BookingRequestDetailsComponent extends RolesAwareComponent implements OnInit {
	title = '';
	buttonName = '';
	bookingRequestId = '';
	canConfirm = false;
	confirmedStatus = false;
	dataLoading = false;
	bookingRequestDetails: BookingRequestDetailObj | null = null;

	readonly forwarder: string[] = [UserRole.FORWARDER];

	constructor(
		private readonly bookingRequestService: BookingRequestService,
		private readonly dialogRef: MatDialogRef<BookingRequestDetailsComponent>,
		private readonly cdr: ChangeDetectorRef,
		private readonly router: Router,
		@Inject(MAT_DIALOG_DATA) public data: any
	) {
		super();
	}

	ngOnInit(): void {
		this.title = this.data.title;
		this.buttonName = this.data.buttonName;
		this.bookingRequestId = this.data.bookingRequestId;
		this.canConfirm = this.data.canConfirm;
		this.confirmedStatus = this.data.confirmedStatus;
		this.dataLoading = true;

		this.bookingRequestService.getBookingRequestDetail(this.bookingRequestId).subscribe({
			next: (res) => {
				this.bookingRequestDetails = res;
				this.handleDataLoading();
			},
			error: () => {
				this.handleDataLoading();
			},
		});
	}

	handleDataLoading(): void {
		this.dataLoading = false;
		this.cdr.markForCheck();
	}

	onDone() {
		if (this.canConfirm && !this.confirmedStatus) {
			this.dataLoading = true;

			this.bookingRequestService.confirmBookingRequest(this.bookingRequestId).subscribe({
				next: () => {
					this.handleDataLoading();
					this.dialogRef.close(true);
				},
				error: () => {
					this.handleDataLoading();
					this.dialogRef.close(false);
				},
			});
		} else {
			this.router.navigate(['/mawb/create'], {
				state: {
					bookingId: this.bookingRequestDetails?.bookingId,
				},
			});
			this.dialogRef.close(false);
		}
	}

	onUpdate() {
		this.router.navigate(['/mawb'], {
			state: {
				bookingId: this.bookingRequestDetails?.bookingId,
			},
		});
		this.dialogRef.close(false);
	}

	onCancel() {
		this.dialogRef.close(false);
	}
}
