@use 'utils';

.iata-login-page {
	min-height: 100vh;
	background: linear-gradient(135deg, var(--iata-blue-primary) 0%, var(--iata-blue-darker) 100%);
	display: flex;
	align-items: center;
	justify-content: center;
	padding: var(--iata-gap-m);

	&__container {
		width: 100%;
		max-width: 480px;
	}

	&__card {
		border-radius: 8px;
		box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
		overflow: hidden;

		.mat-mdc-card-header {
			padding: var(--iata-gap-l) var(--iata-gap-l) 0;
		}

		.mat-mdc-card-content {
			padding: var(--iata-gap-m) var(--iata-gap-l) var(--iata-gap-l);
		}
	}

	&__header {
		background-color: var(--iata-white);
		border-bottom: 1px solid var(--iata-grey-100);
	}

	&__logo-container {
		display: flex;
		align-items: center;
		gap: var(--iata-gap-m);
		width: 100%;
	}

	&__logo {
		width: 60px;
		height: 60px;
		flex-shrink: 0;
	}

	&__title-container {
		flex: 1;
	}

	&__title {
		margin: 0;
		font-size: 24px;
		font-weight: 800;
		color: var(--iata-blue-primary);
		line-height: 1.2;
	}

	&__subtitle {
		margin: 4px 0 0;
		font-size: 14px;
		font-weight: 400;
		color: var(--iata-grey-600);
		line-height: 1.2;
	}

	&__content {
		background-color: var(--iata-white);
	}

	&__form-title {
		margin: 0 0 var(--iata-gap-xs);
		font-size: 28px;
		font-weight: 600;
		color: var(--iata-grey-800);
		text-align: center;
	}

	&__form-subtitle {
		margin: 0 0 var(--iata-gap-l);
		font-size: 16px;
		color: var(--iata-grey-600);
		text-align: center;
		line-height: 1.4;
	}

	&__form {
		display: flex;
		flex-direction: column;
		gap: var(--iata-gap-m);
	}

	&__field {
		width: 100%;

		.mat-mdc-text-field-wrapper {
			background-color: var(--iata-grey-50);
		}

		.mat-mdc-form-field-icon-suffix {
			color: var(--iata-blue-primary);
		}

		&:focus-within {
			.mat-mdc-text-field-wrapper {
				background-color: var(--iata-white);
			}
		}
	}

	&__submit-button {
		width: 100%;
		height: 48px;
		font-size: 16px;
		font-weight: 600;
		margin-top: var(--iata-gap-s);
		border-radius: 4px;

		&:not(:disabled) {
			background-color: var(--iata-blue-primary);
			color: var(--iata-white);

			&:hover {
				background-color: var(--iata-blue-darker);
			}
		}

		&:disabled {
			background-color: var(--iata-grey-200);
			color: var(--iata-grey-400);
		}
	}

	&__loading-icon {
		margin-right: var(--iata-gap-xs);
		animation: spin 1s linear infinite;
	}
}

@keyframes spin {
	0% {
		transform: rotate(0deg);
	}
	100% {
		transform: rotate(360deg);
	}
}

// Responsive design
@include utils.respondBefore(utils.$device-breakpoint-small) {
	.iata-login-page {
		padding: var(--iata-gap-s);

		&__card {
			.mat-mdc-card-header {
				padding: var(--iata-gap-m) var(--iata-gap-m) 0;
			}

			.mat-mdc-card-content {
				padding: var(--iata-gap-m);
			}
		}

		&__logo {
			width: 50px;
			height: 50px;
		}

		&__title {
			font-size: 20px;
		}

		&__form-title {
			font-size: 24px;
		}

		&__form-subtitle {
			font-size: 14px;
		}
	}
}
