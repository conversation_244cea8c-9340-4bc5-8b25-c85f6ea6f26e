export enum ShareType {
	SLI = 'sli',
	MAWB = 'mawb-management',
	HAWB = 'hawb-management',
	SUBSCRIPTION_CONFIG = 'user-subscriptions',
	CHANGE_REQUEST = 'change-request',
}

export enum LogisticObjType {
	SLI = 'SLI',
	MAWB = 'MAWB',
	HAWB = 'HAWB',
	BOOKING_OPTION_REQUEST = 'BOOKING_OPTION_REQUEST',
	BOOKING_REQUEST = 'BOOKING_REQUEST',
	PIECES = 'PIECES',
}

export enum SLISourceType {
	SLI_LIST_CREATE = '0',
	SLI_LIST_EDIT = '1',
	PIECE_LIST = '2',
	ECSD_LIST = '3',
}
