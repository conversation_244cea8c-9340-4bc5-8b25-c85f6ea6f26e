import { Component, inject } from '@angular/core';
import { MainFooterComponent } from '@shared/components/layout/main-footer/main-footer.component';
import { RouterOutlet } from '@angular/router';
import { MainHeaderComponent } from '@shared/components/layout/main-header/main-header.component';
import { BreadcrumbComponent } from '@shared/components/breadcrumb/breadcrumb.component';
import { AuthService } from '@shared/auth/auth.service';
import { AsyncPipe } from '@angular/common';

@Component({
	selector: 'app-root',
	templateUrl: './app.component.html',
	imports: [MainHeaderComponent, RouterOutlet, MainFooterComponent, BreadcrumbComponent, AsyncPipe],
})
export class AppComponent {
	private readonly authService = inject(AuthService);

	get isLoggedIn$() {
		return this.authService.isLoggedIn$;
	}
}
