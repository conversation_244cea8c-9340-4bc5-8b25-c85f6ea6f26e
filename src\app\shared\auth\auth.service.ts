import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable, throwError, BehaviorSubject } from 'rxjs';
import { catchError, tap } from 'rxjs/operators';
import { ApiService } from '@shared/services/api.service';
import { Base64 } from 'js-base64';
import { Router } from '@angular/router';

@Injectable({
	providedIn: 'root',
})
export class AuthService extends ApiService {
	private readonly tokenKey = 'auth_token';
	private payload: any;
	private readonly isLoggedInSubject = new BehaviorSubject<boolean>(this.hasValidToken());

	public readonly isLoggedIn$ = this.isLoggedInSubject.asObservable();

	constructor(
		http: HttpClient,
		private readonly router: Router
	) {
		super(http);
	}

	private hasValidToken(): boolean {
		return !!localStorage.getItem(this.tokenKey);
	}

	login(params?: any): Observable<any> {
		return super.postData<string>('sys-management/login', params).pipe(
			tap((response) => {
				this.setToken(response);
				this.isLoggedInSubject.next(true);
			}),
			catchError((error) => {
				this.isLoggedInSubject.next(false);
				return throwError(() => error);
			})
		);
	}

	getToken(): string | null {
		const token = localStorage.getItem(this.tokenKey);
		if (token && !this.payload) {
			this.payload = this.decodeJwtPayload(token);
		}
		return token;
	}

	setToken(token: string): void {
		this.payload = this.decodeJwtPayload(token);
		localStorage.setItem(this.tokenKey, token);
		this.isLoggedInSubject.next(true);
	}

	clearToken(): void {
		localStorage.removeItem(this.tokenKey);
		this.payload = null;
		this.isLoggedInSubject.next(false);
	}

	isLoggedIn(): boolean {
		return this.hasValidToken();
	}

	logout(): void {
		this.clearToken();
		this.router.navigate(['/login']);
	}

	getPayload(): any {
		return this.payload;
	}

	decodeJwtPayload(payload: string): any {
		if (payload.length === 0) {
			throw new Error('Cannot extract from an empty payload.');
		}

		const parts = payload.split('.');

		if (parts.length !== 3) {
			throw new Error(`The payload ${payload} is not valid JWT payload and must consist of three parts.`);
		}

		let decoded: string;
		try {
			decoded = Base64.decode(parts[1]);
		} catch {
			throw new Error(`The payload ${payload} is not valid JWT payload and cannot be parsed.`);
		}

		if (!decoded) {
			throw new Error(`The payload ${payload} is not valid JWT payload and cannot be decoded.`);
		}
		return JSON.parse(decoded);
	}
}
