import { ComponentFixture, TestBed } from '@angular/core/testing';
import { ReactiveFormsModule } from '@angular/forms';
import { Router } from '@angular/router';
import { NoopAnimationsModule } from '@angular/platform-browser/animations';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { of, throwError } from 'rxjs';

import LoginPageComponent from './login-page.component';
import { AuthService } from '@shared/auth/auth.service';
import { NotificationService } from '@shared/services/notification.service';

describe('LoginPageComponent', () => {
	let component: LoginPageComponent;
	let fixture: ComponentFixture<LoginPageComponent>;
	let authService: jasmine.SpyObj<AuthService>;
	let router: jasmine.SpyObj<Router>;
	let notificationService: jasmine.SpyObj<NotificationService>;

	beforeEach(async () => {
		const authServiceSpy = jasmine.createSpyObj('AuthService', ['login', 'isLoggedIn']);
		const routerSpy = jasmine.createSpyObj('Router', ['navigate']);
		const notificationServiceSpy = jasmine.createSpyObj('NotificationService', ['showSuccess', 'showError']);

		await TestBed.configureTestingModule({
			imports: [
				LoginPageComponent,
				ReactiveFormsModule,
				NoopAnimationsModule,
				TranslateModule.forRoot(),
			],
			providers: [
				{ provide: AuthService, useValue: authServiceSpy },
				{ provide: Router, useValue: routerSpy },
				{ provide: NotificationService, useValue: notificationServiceSpy },
				TranslateService,
			],
		}).compileComponents();

		fixture = TestBed.createComponent(LoginPageComponent);
		component = fixture.componentInstance;
		authService = TestBed.inject(AuthService) as jasmine.SpyObj<AuthService>;
		router = TestBed.inject(Router) as jasmine.SpyObj<Router>;
		notificationService = TestBed.inject(NotificationService) as jasmine.SpyObj<NotificationService>;

		authService.isLoggedIn.and.returnValue(false);
		fixture.detectChanges();
	});

	it('should create', () => {
		expect(component).toBeTruthy();
	});

	it('should initialize form with empty values', () => {
		expect(component.loginForm.get('email')?.value).toBe('');
		expect(component.loginForm.get('password')?.value).toBe('');
	});

	it('should validate required fields', () => {
		const emailControl = component.loginForm.get('email');
		const passwordControl = component.loginForm.get('password');

		expect(emailControl?.hasError('required')).toBeTruthy();
		expect(passwordControl?.hasError('required')).toBeTruthy();
	});

	it('should validate email format', () => {
		const emailControl = component.loginForm.get('email');
		emailControl?.setValue('invalid-email');
		expect(emailControl?.hasError('email')).toBeTruthy();

		emailControl?.setValue('<EMAIL>');
		expect(emailControl?.hasError('email')).toBeFalsy();
	});

	it('should validate password minimum length', () => {
		const passwordControl = component.loginForm.get('password');
		passwordControl?.setValue('123');
		expect(passwordControl?.hasError('minlength')).toBeTruthy();

		passwordControl?.setValue('123456');
		expect(passwordControl?.hasError('minlength')).toBeFalsy();
	});

	it('should call login service on valid form submission', () => {
		authService.login.and.returnValue(of('mock-token'));
		
		component.loginForm.patchValue({
			email: '<EMAIL>',
			password: 'password123'
		});

		component.onSubmit();

		expect(authService.login).toHaveBeenCalledWith({
			userId: '<EMAIL>',
			orgId: 'ddbab271-1ddd-4cc4-b546-af9a0c0d4943'
		});
	});

	it('should navigate to return URL on successful login', () => {
		authService.login.and.returnValue(of('mock-token'));
		component.returnUrl = '/dashboard';
		
		component.loginForm.patchValue({
			email: '<EMAIL>',
			password: 'password123'
		});

		component.onSubmit();

		expect(router.navigate).toHaveBeenCalledWith(['/dashboard']);
		expect(notificationService.showSuccess).toHaveBeenCalledWith('auth.login.success');
	});

	it('should show error message on login failure', () => {
		authService.login.and.returnValue(throwError(() => new Error('Login failed')));
		
		component.loginForm.patchValue({
			email: '<EMAIL>',
			password: 'password123'
		});

		component.onSubmit();

		expect(notificationService.showError).toHaveBeenCalledWith('auth.login.failed');
	});

	it('should toggle password visibility', () => {
		expect(component.hidePassword).toBeTruthy();
		
		component.togglePasswordVisibility();
		expect(component.hidePassword).toBeFalsy();
		
		component.togglePasswordVisibility();
		expect(component.hidePassword).toBeTruthy();
	});

	it('should redirect if already logged in', () => {
		authService.isLoggedIn.and.returnValue(true);
		component.returnUrl = '/dashboard';
		
		component.ngOnInit();
		
		expect(router.navigate).toHaveBeenCalledWith(['/dashboard']);
	});
});
