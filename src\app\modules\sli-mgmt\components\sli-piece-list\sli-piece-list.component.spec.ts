import { ComponentFixture, TestBed, fakeAsync, tick, discardPeriodicTasks } from '@angular/core/testing';
import { SliPieceListComponent } from './sli-piece-list.component';
import { TranslateModule } from '@ngx-translate/core';
import { ReactiveFormsModule } from '@angular/forms';
import { NoopAnimationsModule } from '@angular/platform-browser/animations';
import { SliCreateRequestService } from '../../services/sli-create-request.service';
import { of, throwError } from 'rxjs';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatSelectModule } from '@angular/material/select';
import { Sort } from '@angular/material/sort';
import { PageEvent } from '@angular/material/paginator';
import { PieceList } from '../../models/piece/piece-list.model';
import { ChangeDetectorRef } from '@angular/core';
import { provideHttpClient } from '@angular/common/http';
import { provideHttpClientTesting } from '@angular/common/http/testing';
import { ActivatedRoute, Router } from '@angular/router';

describe('SliPieceListComponent', () => {
	let component: SliPieceListComponent;
	let fixture: ComponentFixture<SliPieceListComponent>;
	let mockSliCreateRequestService: jasmine.SpyObj<SliCreateRequestService>;
	let mockChangeDetectorRef: jasmine.SpyObj<ChangeDetectorRef>;
	let mockRouter: jasmine.SpyObj<Router>;
	let mockActivatedRoute: jasmine.SpyObj<ActivatedRoute>;

	const mockPieceListResponse = {
		rows: [
			{
				type: 'Piece',
				pieceId: 'TEST123',
				productDescription: '123',
				packagingType: '456',
				grossWeight: 10,
				dimensions: {
					length: 10,
					width: 10,
					height: 10,
				},
				pieceQuantity: 1,
				slac: 0,
				level: 0,
				expanded: false,
				containedPieces: [
					{
						type: 'Piece',
						pieceId: 'contained1',
						productDescription: 'contained piece 1',
						packagingType: 'box',
						grossWeight: 5,
						dimensions: { length: 5, width: 5, height: 5 },
						pieceQuantity: 1,
						slac: 0,
						level: 1,
						expanded: false,
					},
					{
						type: 'Piece',
						pieceId: 'contained2',
						productDescription: 'contained piece 2',
						packagingType: 'box',
						grossWeight: 5,
						dimensions: { length: 5, width: 5, height: 5 },
						pieceQuantity: 1,
						slac: 0,
						level: 1,
						expanded: false,
					},
				] as PieceList[],
			},
			{
				type: 'Piece',
				pieceId: 'TEST456',
				productDescription: '456',
				packagingType: '789',
				grossWeight: 10,
				dimensions: {
					length: 10,
					width: 10,
					height: 10,
				},
				pieceQuantity: 1,
				slac: 0,
				level: 0,
				expanded: false,
				containedPieces: [],
			},
		] as PieceList[],
		total: 2,
	};

	beforeEach(async () => {
		// Create spy objects with all required methods
		mockSliCreateRequestService = jasmine.createSpyObj<SliCreateRequestService>('SliCreateRequestService', [
			'getCurrencies',
			'getIncoterms',
			'getPieceList',
			'getTotalPieceQuantity',
			'getSliTemplate',
		]);
		mockChangeDetectorRef = jasmine.createSpyObj<ChangeDetectorRef>('ChangeDetectorRef', ['markForCheck']);
		mockRouter = jasmine.createSpyObj<Router>('Router', ['navigate']);
		mockActivatedRoute = {
			snapshot: { params: {} },
		} as any;

		// Configure mock return values
		mockSliCreateRequestService.getPieceList.and.returnValue(of(mockPieceListResponse));
		mockSliCreateRequestService.getTotalPieceQuantity.and.returnValue(of({ totalQuantity: 3, totalSlac: 1 }));
		mockSliCreateRequestService.getSliTemplate.and.returnValue(of('TEST-TEMPLATE-123'));

		await TestBed.configureTestingModule({
			imports: [
				SliPieceListComponent,
				TranslateModule.forRoot(),
				ReactiveFormsModule,
				NoopAnimationsModule,
				MatFormFieldModule,
				MatInputModule,
				MatSelectModule,
			],
			providers: [
				{ provide: SliCreateRequestService, useValue: mockSliCreateRequestService },
				{ provide: ChangeDetectorRef, useValue: mockChangeDetectorRef },
				{ provide: Router, useValue: mockRouter },
				{ provide: ActivatedRoute, useValue: mockActivatedRoute },
				provideHttpClient(),
				provideHttpClientTesting(),
			],
		}).compileComponents();

		fixture = TestBed.createComponent(SliPieceListComponent);
		component = fixture.componentInstance;
		fixture.detectChanges();
	});

	describe('Component Initialization', () => {
		it('should create the component', () => {
			expect(component).toBeTruthy();
		});

		it('should fetch piece list when sliNumber is provided', fakeAsync(() => {
			component.sliNumber = 'TEST-SLI-123';
			component.selectedTabIndex = 1;

			component.ngOnChanges({
				selectedTabIndex: {
					currentValue: 1,
					previousValue: 0,
					firstChange: true,
					isFirstChange: () => true,
				},
			});
			tick(2000); // Wait for the setTimeout to complete

			expect(mockSliCreateRequestService.getPieceList).toHaveBeenCalledWith(component.pageParams, 'TEST-SLI-123');
			expect(component.pieceList.length).toBe(2);
			expect(component.pieceList[0].slac).toBe(2); // containedPieces.length
			expect(component.pieceList[1].slac).toBe(0); // empty containedPieces
			expect(component.totalRecords).toBe(mockPieceListResponse.total);

			discardPeriodicTasks(); // Clean up any pending timers
		}));

		it('should fetch piece list when sliTemplateNum is provided but sliNumber is not', fakeAsync(() => {
			component.sliNumber = '';
			component.sliTemplateNum = '123';
			component.selectedTabIndex = 1;

			component.ngOnChanges({
				selectedTabIndex: {
					currentValue: 1,
					previousValue: 0,
					firstChange: true,
					isFirstChange: () => true,
				},
			});
			tick(2000); // Wait for the setTimeout to complete

			expect(mockSliCreateRequestService.getPieceList).toHaveBeenCalledWith(component.pageParams, '123');

			discardPeriodicTasks(); // Clean up any pending timers
		}));
	});

	describe('Sorting and Pagination', () => {
		it('should update pageParams when onSortChange is called with direction', () => {
			const sort: Sort = { active: 'productDescription', direction: 'asc' };

			component.onSortChange(sort);

			expect(component.pageParams.orderByColumn).toBe('productDescription');
			expect(component.pageParams.isAsc).toBe('asc');
		});

		it('should clear sort parameters when onSortChange is called with empty direction', () => {
			// First set some values
			component.pageParams.orderByColumn = 'productDescription';
			component.pageParams.isAsc = 'asc';

			// Then clear them
			const sort: Sort = { active: 'productDescription', direction: '' };
			component.onSortChange(sort);

			expect(component.pageParams.orderByColumn).toBe('');
			expect(component.pageParams.isAsc).toBe('');
		});

		it('should update pageParams and fetch data when onPageChange is called', () => {
			spyOn<any>(component, 'getPieceListPage').and.callThrough();

			const pageEvent: PageEvent & { sortField?: string; sortDirection?: string } = {
				pageIndex: 2,
				pageSize: 50,
				length: 100,
				sortField: 'grossWeight',
				sortDirection: 'desc',
			};

			component.onPageChange(pageEvent);

			expect(component.pageParams.pageNum).toBe(3); // pageIndex + 1
			expect(component.pageParams.pageSize).toBe(50);
			expect(component.pageParams.orderByColumn).toBe('grossWeight');
			expect(component.pageParams.isAsc).toBe('desc');
			expect(component['getPieceListPage']).toHaveBeenCalledWith(component.pageParams);
		});

		it('should update pageParams without sort parameters when onPageChange is called without sort data', () => {
			spyOn<any>(component, 'getPieceListPage').and.callThrough();

			const pageEvent: PageEvent = {
				pageIndex: 2,
				pageSize: 50,
				length: 100,
			};

			component.onPageChange(pageEvent);

			expect(component.pageParams.pageNum).toBe(3); // pageIndex + 1
			expect(component.pageParams.pageSize).toBe(50);
			expect(component['getPieceListPage']).toHaveBeenCalledWith(component.pageParams);
		});
	});

	describe('getPieceListPage Method', () => {
		it('should fetch piece list data and update component properties', fakeAsync(() => {
			component.sliTemplateNum = 'TEST-SLI-123';
			component.dataLoading = false;
			component.pieceList = [];
			component.totalRecords = 0;

			component['getPieceListPage'](component.pageParams);
			tick(); // Wait for all observables to complete

			// Verify the main data was processed
			expect(mockSliCreateRequestService.getPieceList).toHaveBeenCalledWith(component.pageParams, 'TEST-SLI-123');
			expect(component.pieceList.length).toBe(2);
			expect(component.pieceList[0].slac).toBe(2); // containedPieces.length
			expect(component.pieceList[1].slac).toBe(0); // empty containedPieces
			expect(component.totalRecords).toBe(mockPieceListResponse.total);
			expect(component.dataLoading).toBe(false);

			// Verify the nested call was made
			expect(mockSliCreateRequestService.getTotalPieceQuantity).toHaveBeenCalledWith('TEST-SLI-123');
			expect(component.totalQuantity).toBe(3);
			expect(component.totalSlac).toBe(1);
		}));

		it('should handle error when fetching piece list data', fakeAsync(() => {
			mockSliCreateRequestService.getPieceList.and.returnValue(throwError(() => new Error('Test error')));

			component.sliNumber = 'TEST-SLI-123';
			component.dataLoading = false;

			component['getPieceListPage'](component.pageParams);
			tick();

			expect(component.dataLoading).toBe(false);
		}));

		it('should handle 403 error and trigger delegation request', fakeAsync(() => {
			const error403 = {
				status: 403,
				error: {
					data: 'LO123',
					code: 403,
					msg: 'Permission denied',
				},
			};
			mockSliCreateRequestService.getPieceList.and.returnValue(throwError(() => error403));
			spyOn<any>(component, 'delegationRequest').and.returnValue(of(true));

			component.sliNumber = 'TEST-SLI-123';
			component.sliTemplateNum = 'TEST-SLI-123';

			component['getPieceListPage'](component.pageParams);
			tick();

			expect(component.dataLoading).toBe(false);
			expect(component['delegationRequest']).toHaveBeenCalledWith(error403.error, 'TEST-SLI-123');
		}));

		it('should not call getTotalPieceQuantity when sliTemplateNum is empty', fakeAsync(() => {
			component.sliTemplateNum = '';

			component['getPieceListPage'](component.pageParams);
			tick();

			expect(mockSliCreateRequestService.getTotalPieceQuantity).not.toHaveBeenCalled();
		}));
	});

	describe('refreshData Method', () => {
		it('should call getPieceListPageBySliTemplateNum when selectedTabIndex is 1', fakeAsync(() => {
			spyOn<any>(component, 'getPieceListPageBySliTemplateNum');
			component.selectedTabIndex = 1;

			component.refreshData();
			tick(2000);

			expect(component['getPieceListPageBySliTemplateNum']).toHaveBeenCalled();
			discardPeriodicTasks();
		}));

		it('should not call getPieceListPageBySliTemplateNum when selectedTabIndex is not 1', fakeAsync(() => {
			spyOn<any>(component, 'getPieceListPageBySliTemplateNum');
			component.selectedTabIndex = 0;

			component.refreshData();
			tick(2000);

			expect(component['getPieceListPageBySliTemplateNum']).toHaveBeenCalled();
			discardPeriodicTasks();
		}));
	});

	describe('getPieceListPageBySliTemplateNum Method', () => {
		it('should use sliNumber when provided', () => {
			spyOn<any>(component, 'getPieceListPage');
			component.sliNumber = 'TEST-SLI-123';
			component.sliTemplateNum = '';

			component['getPieceListPageBySliTemplateNum']();

			expect(component.sliTemplateNum).toBe('TEST-SLI-123');
			expect(component['getPieceListPage']).toHaveBeenCalledWith(component.pageParams);
		});

		it('should use existing sliTemplateNum when sliNumber is not provided', () => {
			spyOn<any>(component, 'getPieceListPage');
			component.sliNumber = '';
			component.sliTemplateNum = 'EXISTING-TEMPLATE';

			component['getPieceListPageBySliTemplateNum']();

			expect(component.sliTemplateNum).toBe('EXISTING-TEMPLATE');
			expect(component['getPieceListPage']).toHaveBeenCalledWith(component.pageParams);
		});

		it('should fetch template when neither sliNumber nor sliTemplateNum is provided', fakeAsync(() => {
			spyOn<any>(component, 'getPieceListPage');
			component.sliNumber = '';
			component.sliTemplateNum = '';

			component['getPieceListPageBySliTemplateNum']();
			tick();

			expect(mockSliCreateRequestService.getSliTemplate).toHaveBeenCalled();
			expect(component.sliTemplateNum).toBe('TEST-TEMPLATE-123');
			expect(component['getPieceListPage']).toHaveBeenCalledWith(component.pageParams);
		}));
	});

	describe('getFormData Method', () => {
		it('should return object with empty pieces array', () => {
			const result = component.getFormData();

			expect(result).toEqual({ pieces: [] });
		});

		it('should return object with empty pieces array when ignore parameter is provided', () => {
			const result = component.getFormData(true);

			expect(result).toEqual({ pieces: [] });
		});
	});

	describe('createOrUpdatePiece Method', () => {
		it('should navigate to piece creation route when pieceId is not provided', () => {
			const pieceParams = { pieceType: 'standard' };

			component.createOrUpdatePiece(pieceParams);

			expect(mockRouter.navigate).toHaveBeenCalledWith(['piece', 'standard'], { relativeTo: mockActivatedRoute });
		});

		it('should navigate to piece edit route when pieceId is provided', () => {
			const pieceParams = { pieceType: 'standard', pieceId: 'PIECE-123' };

			component.createOrUpdatePiece(pieceParams);

			expect(mockRouter.navigate).toHaveBeenCalledWith(['piece', 'standard', 'PIECE-123'], { relativeTo: mockActivatedRoute });
		});
	});

	describe('Component Properties and Inputs', () => {
		it('should initialize with default values', () => {
			expect(component.pieceList).toEqual([]);
			expect(component.totalRecords).toBe(0);
			expect(component.totalQuantity).toBe(0);
			expect(component.totalSlac).toBe(0);
			expect(component.dataLoading).toBe(false);
			expect(component.sliTemplateNum).toBe('');
			expect(component.sliNumber).toBe('');
			expect(component.selectedTabIndex).toBe(0);
			expect(component.isForHawb).toBe(false);
			expect(component.pageParams).toEqual({
				pageNum: 1,
				pageSize: 10,
			});
		});

		it('should handle input changes correctly', () => {
			component.sliNumber = 'NEW-SLI-456';
			component.selectedTabIndex = 2;
			component.isForHawb = true;

			expect(component.sliNumber).toBe('NEW-SLI-456');
			expect(component.selectedTabIndex).toBe(2);
			expect(component.isForHawb).toBe(true);
		});
	});

	describe('Event Emitters', () => {
		it('should emit saveRequest event', () => {
			spyOn(component.saveRequest, 'emit');
			const testData = 'test-data';

			component.saveRequest.emit(testData);

			expect(component.saveRequest.emit).toHaveBeenCalledWith(testData);
		});

		it('should emit refreshDelegationRequest event', () => {
			spyOn(component.refreshDelegationRequest, 'emit');

			component.refreshDelegationRequest.emit();

			expect(component.refreshDelegationRequest.emit).toHaveBeenCalled();
		});
	});

	describe('Data Transformation', () => {
		it('should correctly transform piece list data with slac calculation', fakeAsync(() => {
			const mockResponseWithVariousContainedPieces = {
				rows: [
					{
						...mockPieceListResponse.rows[0],
						containedPieces: [
							{ pieceId: '1', pieceQuantity: 1 },
							{ pieceId: '2', pieceQuantity: 1 },
							{ pieceId: '3', pieceQuantity: 3 },
						] as PieceList[],
					},
					{
						...mockPieceListResponse.rows[1],
						containedPieces: undefined,
					},
				] as PieceList[],
				total: 2,
			};

			mockSliCreateRequestService.getPieceList.and.returnValue(of(mockResponseWithVariousContainedPieces));
			component.sliTemplateNum = 'TEST-SLI-123';

			component['getPieceListPage'](component.pageParams);
			tick();

			expect(component.pieceList[0].slac).toBe(5); // 5 contained pieces
			expect(component.pieceList[1].slac).toBe(0); // undefined containedPieces
			expect(component.pieceList[0].level).toBe(0);
			expect(component.pieceList[0].expanded).toBe(false);
		}));
	});

	describe('Error Handling', () => {
		it('should handle non-403 errors gracefully', fakeAsync(() => {
			const error500 = { status: 500, error: 'Internal Server Error' };
			mockSliCreateRequestService.getPieceList.and.returnValue(throwError(() => error500));
			spyOn<any>(component, 'delegationRequest');

			component.sliTemplateNum = 'TEST-SLI-123';

			component['getPieceListPage'](component.pageParams);
			tick();

			expect(component.dataLoading).toBe(false);
			expect(component['delegationRequest']).not.toHaveBeenCalled();
		}));
	});
});
