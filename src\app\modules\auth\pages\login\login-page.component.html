<div class="iata-login-page">
	<div class="iata-login-page__container">
		<mat-card class="iata-login-page__card">
			<mat-card-header class="iata-login-page__header">
				<div class="iata-login-page__logo-container">
					<img src="assets/images/iata-logo.svg" class="iata-login-page__logo" alt="IATA logo" />
					<div class="iata-login-page__title-container">
						<h1 class="iata-login-page__title">{{ 'common.mainHeader.app.title' | translate }}</h1>
						<p class="iata-login-page__subtitle">{{ 'common.mainHeader.app.subtitle' | translate }}</p>
					</div>
				</div>
			</mat-card-header>

			<mat-card-content class="iata-login-page__content">
				<h2 class="iata-login-page__form-title">{{ 'auth.login.title' | translate }}</h2>
				<p class="iata-login-page__form-subtitle">{{ 'auth.login.subtitle' | translate }}</p>

				<form [formGroup]="loginForm" (ngSubmit)="onSubmit()" class="iata-login-page__form">
					<mat-form-field appearance="outline" class="iata-login-page__field">
						<mat-label>{{ 'auth.login.email.label' | translate }}</mat-label>
						<input
							matInput
							type="email"
							formControlName="email"
							[placeholder]="'auth.login.email.placeholder' | translate"
							autocomplete="email"
						/>
						<mat-icon matSuffix>email</mat-icon>
						@if (loginForm.get('email')?.invalid && loginForm.get('email')?.touched) {
							<mat-error>{{ getEmailErrorMessage() | translate }}</mat-error>
						}
					</mat-form-field>

					<mat-form-field appearance="outline" class="iata-login-page__field">
						<mat-label>{{ 'auth.login.password.label' | translate }}</mat-label>
						<input
							matInput
							[type]="hidePassword ? 'password' : 'text'"
							formControlName="password"
							[placeholder]="'auth.login.password.placeholder' | translate"
							autocomplete="current-password"
						/>
						<button
							mat-icon-button
							matSuffix
							type="button"
							(click)="togglePasswordVisibility()"
							[attr.aria-label]="'auth.login.password.toggle' | translate"
							[attr.aria-pressed]="!hidePassword"
						>
							<mat-icon>{{ hidePassword ? 'visibility_off' : 'visibility' }}</mat-icon>
						</button>
						@if (loginForm.get('password')?.invalid && loginForm.get('password')?.touched) {
							<mat-error>{{ getPasswordErrorMessage() | translate }}</mat-error>
						}
					</mat-form-field>

					<button
						mat-flat-button
						color="primary"
						type="submit"
						class="iata-login-page__submit-button"
						[disabled]="isLoading"
					>
						@if (isLoading) {
							<mat-icon class="iata-login-page__loading-icon">hourglass_empty</mat-icon>
						}
						{{ 'auth.login.submit' | translate }}
					</button>
				</form>
			</mat-card-content>
		</mat-card>
	</div>
</div>
