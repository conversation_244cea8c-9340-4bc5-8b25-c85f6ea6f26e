import { ComponentFixture, TestBed, fakeAsync, tick } from '@angular/core/testing';
import { SliTableComponent } from './sli-table.component';
import { CUSTOM_ELEMENTS_SCHEMA, NO_ERRORS_SCHEMA, SimpleChange } from '@angular/core';
import { SliListObject } from '../../models/sli-list-object.model';
import { ActivatedRoute, Router, provideRouter } from '@angular/router';
import { TranslateModule } from '@ngx-translate/core';
import { MatPaginator, PageEvent } from '@angular/material/paginator';
import { Sort } from '@angular/material/sort';
import { NoopAnimationsModule } from '@angular/platform-browser/animations';
import { By } from '@angular/platform-browser';
import { provideHttpClient, withInterceptorsFromDi } from '@angular/common/http';
import { provideHttpClientTesting } from '@angular/common/http/testing';
import { UserProfileService } from '@shared/services/user-profile.service';
import { of } from 'rxjs';
import { MatDialog } from '@angular/material/dialog';
import { SLISourceType } from '@shared/models/share-type.model';

describe('SliTableComponent', () => {
	let component: SliTableComponent;
	let fixture: ComponentFixture<SliTableComponent>;
	let router: Router;
	let userProfileServiceSpy: jasmine.SpyObj<UserProfileService>;
	let activatedRoute: ActivatedRoute;
	let mockDialog: jasmine.SpyObj<MatDialog>;

	// Mock data
	const mockRecords: SliListObject[] = [
		{
			sliNumber: '2b7f9542-8e28-4d1f-b30b-ac0eac97db5d',
			waybillNumber: 'S000920',
			shipper: 'Shipper A',
			consignee: 'Consignee A',
			goodsDescription: 'Goods A',
			departureLocation: 'DEP A',
			arrivalLocation: 'ARR A',
			hawbNumber: 'H1',
			createDate: '2023-04-13',
		},
		{
			sliNumber: '2b7f9542-8e28-4d1f-b30b-ac0eac97db4d',
			waybillNumber: 'S000921',
			shipper: 'Shipper B',
			consignee: 'Consignee B',
			goodsDescription: 'Goods B',
			departureLocation: 'DEP B',
			arrivalLocation: 'ARR B',
			hawbNumber: 'H2',
			createDate: '2023-04-14',
		},
		{
			sliNumber: '2b7f9542-8e28-4d1f-b30b-ac0eac97db3d',
			waybillNumber: 'S000922',
			shipper: 'Shipper C',
			consignee: 'Consignee C',
			goodsDescription: 'Goods C',
			departureLocation: 'DEP C',
			arrivalLocation: 'ARR C',
			hawbNumber: 'H3',
			createDate: '2023-04-15',
		},
	];

	// Mock data with missing fields
	const mockIncompleteRecords: SliListObject[] = [
		{
			sliNumber: '2b7f9542-8e28-4d1f-b30b-ac0eac97db2d',
			waybillNumber: 'S000923',
			shipper: '',
			consignee: 'Consignee D',
			goodsDescription: 'Goods D',
			departureLocation: 'DEP D',
			arrivalLocation: 'ARR D',
			hawbNumber: '',
			createDate: '2023-04-16',
		},
		{
			sliNumber: '2b7f9542-8e28-4d1f-b30b-ac0eac97db1d',
			waybillNumber: 'S000924',
			shipper: 'Shipper E',
			consignee: '',
			goodsDescription: '',
			departureLocation: 'DEP E',
			arrivalLocation: 'ARR E',
			hawbNumber: 'H5',
			createDate: '2023-04-17',
		},
	];

	beforeEach(async () => {
		mockDialog = jasmine.createSpyObj('MatDialog', ['open']);
		userProfileServiceSpy = jasmine.createSpyObj('UserProfileService', ['hasPermission', 'hasSomeRole', 'getProfile']);
		userProfileServiceSpy.hasPermission.and.returnValue(of(true));
		userProfileServiceSpy.hasSomeRole.and.returnValue(of(true));

		await TestBed.configureTestingModule({
			imports: [SliTableComponent, TranslateModule.forRoot(), NoopAnimationsModule],
			schemas: [CUSTOM_ELEMENTS_SCHEMA, NO_ERRORS_SCHEMA],
			providers: [
				provideRouter([]),
				{ provide: MatDialog, useValue: mockDialog },
				{
					provide: ActivatedRoute,
					useClass: class ActivatedRouteMock {},
				},
				{
					provide: UserProfileService,
					useValue: userProfileServiceSpy,
				},
				provideHttpClient(withInterceptorsFromDi()),
				provideHttpClientTesting(),
			],
		}).compileComponents();

		router = TestBed.inject(Router);
		activatedRoute = TestBed.inject(ActivatedRoute);

		// Spy on router.navigate method
		spyOn(router, 'navigate');
	});

	beforeEach(() => {
		fixture = TestBed.createComponent(SliTableComponent);
		component = fixture.componentInstance;

		// Initialize required pagination params
		component.pageParams = {
			pageNum: 1,
			pageSize: 10,
			orderByColumn: '',
			isAsc: 'asc',
		};

		fixture.detectChanges();
	});

	describe('Component Initialization', () => {
		it('should create the component', () => {
			expect(component).toBeTruthy();
		});

		it('should initialize with empty records array', () => {
			expect(component.records).toEqual([]);
			expect(component.dataSource.data).toEqual([]);
		});

		it('should initialize with correct page size options', () => {
			expect(component.tablePageSizes).toEqual([10, 50, 100]);
		});
	});

	describe('Data Handling', () => {
		it('should update dataSource when records input changes', () => {
			component.records = mockRecords;
			component.ngOnChanges({ records: new SimpleChange(null, mockRecords, true) });
			expect(component.dataSource.data).toEqual(mockRecords);
		});

		it('should not update dataSource when other inputs change', () => {
			component.records = mockRecords;
			component.ngOnChanges({ records: new SimpleChange(null, mockRecords, true) });

			// Set initial state
			expect(component.dataSource.data).toEqual(mockRecords);

			// Change totalRecords
			component.totalRecords = 100;
			component.ngOnChanges({ totalRecords: new SimpleChange(0, 100, true) });

			// DataSource should remain unchanged
			expect(component.dataSource.data).toEqual(mockRecords);
		});

		it('should generate correct tracking key with trackBySliCode', () => {
			const record = mockRecords[0];
			const key = component.trackBySliCode(0, record);
			expect(key).toEqual('S0009202023-04-13');
		});

		it('should handle empty records array', () => {
			component.records = [];
			component.ngOnChanges({ records: new SimpleChange(mockRecords, [], false) });
			expect(component.dataSource.data).toEqual([]);
		});

		it('should handle records with missing data', () => {
			component.records = mockIncompleteRecords;
			component.ngOnChanges({ records: new SimpleChange(null, mockIncompleteRecords, true) });
			expect(component.dataSource.data).toEqual(mockIncompleteRecords);

			// Verify the table renders correctly with incomplete data
			fixture.detectChanges();
			const rows = fixture.debugElement.queryAll(By.css('tr.orll-sli-table__row'));
			expect(rows.length).toBe(mockIncompleteRecords.length);
		});

		it('should handle null records gracefully', () => {
			component.records = mockRecords;
			component.ngOnChanges({ records: new SimpleChange(mockRecords, null, false) });
			// When null is passed, the component should default to an empty array
			expect(component.dataSource.data).toEqual(mockRecords);
		});
	});

	describe('Navigation', () => {
		it('should navigate to edit route with waybill number when editSli is called', () => {
			const waybillNumber = 'S000920';
			component.editSli(waybillNumber);
			expect(router.navigate).toHaveBeenCalledWith(['edit', waybillNumber, 0, { source: SLISourceType.SLI_LIST_EDIT }], {
				relativeTo: activatedRoute,
			});
		});

		it('should navigate to edit route with empty waybill number when editSli is called with empty string', () => {
			const waybillNumber = '';
			component.editSli(waybillNumber);
			expect(router.navigate).toHaveBeenCalledWith(['edit', waybillNumber, 0, { source: SLISourceType.SLI_LIST_EDIT }], {
				relativeTo: activatedRoute,
			});
		});
	});

	describe('Event Handling', () => {
		it('should emit shareSli event when share button is clicked', () => {
			// Setup
			component.records = mockRecords;
			component.ngOnChanges({ records: new SimpleChange(null, mockRecords, true) });
			fixture.detectChanges();

			// Spy on the output event
			spyOn(component.shareSli, 'emit');

			// Find and click the first share button
			const shareButtons = fixture.debugElement.queryAll(By.css('button.share-button'));
			expect(shareButtons.length).toBeGreaterThan(0);
			shareButtons[0].nativeElement.click();

			// Verify the event was emitted with the correct record
			expect(component.shareSli.emit).toHaveBeenCalledWith(mockRecords[0]);
		});

		it('should emit sortChange event when sort is changed', fakeAsync(() => {
			// Setup
			spyOn(component.sortChange, 'emit');

			// Manually trigger the sort event
			const sortEvent: Sort = { active: 'waybillNumber', direction: 'asc' };
			component.onSortChange(sortEvent);
			tick();

			// Verify the event was emitted with the correct sort parameters
			expect(component.sortChange.emit).toHaveBeenCalledWith(sortEvent);
		}));

		it('should emit pagination event when page is changed', () => {
			// Setup
			spyOn(component.pagination, 'emit');

			// Create a page event
			const pageEvent: PageEvent = {
				pageIndex: 1,
				pageSize: 50,
				length: 100,
			};

			// Get the paginator and trigger the page event
			const paginator = fixture.debugElement.query(By.directive(MatPaginator));
			if (paginator) {
				const paginatorComponent = paginator.componentInstance as MatPaginator;
				paginatorComponent.page.emit(pageEvent);

				// Verify the event was emitted with the correct page parameters
				expect(component.pagination.emit).toHaveBeenCalledWith(pageEvent);
			}
		});

		it('should emit pagination with sort information when emitPaginationWithSort is called', fakeAsync(() => {
			// Setup
			spyOn(component.pagination, 'emit');

			// Set current sort
			const sortEvent: Sort = { active: 'shipper', direction: 'desc' };
			component.currentSort = sortEvent;

			// Call the private method using type assertion
			(component as any).emitPaginationWithSort();
			tick();

			// Verify the event was emitted with the correct parameters
			expect(component.pagination.emit).toHaveBeenCalledWith({
				pageIndex: component.pageParams.pageNum - 1,
				pageSize: component.pageParams.pageSize,
				length: component.totalRecords,
				sortField: 'shipper',
				sortDirection: 'desc',
			});
		}));

		it('should emit pagination with sort and custom page event when emitPaginationWithSort is called with page event', fakeAsync(() => {
			// Setup
			spyOn(component.pagination, 'emit');

			// Set current sort
			const sortEvent: Sort = { active: 'consignee', direction: 'asc' };
			component.currentSort = sortEvent;

			// Create a page event
			const pageEvent: PageEvent = {
				pageIndex: 2,
				pageSize: 25,
				length: 150,
			};

			// Call the private method using type assertion
			(component as any).emitPaginationWithSort(pageEvent);
			tick();

			// Verify the event was emitted with the correct parameters
			expect(component.pagination.emit).toHaveBeenCalledWith({
				...pageEvent,
				sortField: 'consignee',
				sortDirection: 'asc',
			});
		}));
	});

	describe('UI Elements', () => {
		beforeEach(() => {
			component.records = mockRecords;
			component.ngOnChanges({ records: new SimpleChange(null, mockRecords, true) });
			fixture.detectChanges();
		});

		it('should display the correct number of rows in the table', () => {
			const rows = fixture.debugElement.queryAll(By.css('tr.orll-sli-table__row'));
			expect(rows.length).toBe(mockRecords.length);
		});

		it('should display the create button with correct text', () => {
			const createButton = fixture.debugElement.query(By.css('button[color="primary"]'));
			expect(createButton).toBeTruthy();
			// Note: We can't check the text directly due to the translation pipe
		});

		it('should call createSli when create button is clicked', () => {
			spyOn(component, 'createSli');
			const createButton = fixture.debugElement.queryAll(By.css('button[color="primary"]'));
			createButton[1].nativeElement.click();
			expect(component.createSli).toHaveBeenCalled();
		});

		it('should call editSli when a waybill number link is clicked', () => {
			spyOn(component, 'editSli');
			const waybillLinks = fixture.debugElement.queryAll(By.css('.sli-number__link'));
			expect(waybillLinks.length).toBeGreaterThan(0);
			waybillLinks[0].nativeElement.click();
			expect(component.editSli).toHaveBeenCalledWith(mockRecords[0].sliNumber);
		});

		it('should display all table columns correctly', () => {
			const headerCells = fixture.debugElement.queryAll(By.css('th[mat-header-cell]'));
			expect(headerCells.length).toBe(component.displayedColumns.length);
		});

		it('should display correct data in each cell', () => {
			const firstRow = fixture.debugElement.queryAll(By.css('tr.orll-sli-table__row'))[0];
			const cells = firstRow.queryAll(By.css('td'));

			// Check waybill number (first column)
			expect(cells[0].query(By.css('.sli-number__link')).nativeElement.textContent.trim()).toBe(mockRecords[0].waybillNumber);

			// Check shipper (second column)
			expect(cells[1].nativeElement.textContent.trim()).toBe(mockRecords[0].shipper);

			// Check consignee (third column)
			expect(cells[2].nativeElement.textContent.trim()).toBe(mockRecords[0].consignee);
		});
	});

	describe('Edge Cases', () => {
		it('should handle empty string values in records', () => {
			component.records = mockIncompleteRecords;
			component.ngOnChanges({ records: new SimpleChange(null, mockIncompleteRecords, true) });
			fixture.detectChanges();

			// Get the first row (which has empty shipper)
			const firstRow = fixture.debugElement.queryAll(By.css('tr.orll-sli-table__row'))[0];
			const cells = firstRow.queryAll(By.css('td'));

			// Check shipper cell (second column) - should be empty
			expect(cells[1].nativeElement.textContent.trim()).toBe('');

			// Get the second row (which has empty consignee and goodsDescription)
			const secondRow = fixture.debugElement.queryAll(By.css('tr.orll-sli-table__row'))[1];
			const secondRowCells = secondRow.queryAll(By.css('td'));

			// Check consignee cell (third column) - should be empty
			expect(secondRowCells[2].nativeElement.textContent.trim()).toBe('');

			// Check goodsDescription cell (fourth column) - should be empty
			expect(secondRowCells[3].nativeElement.textContent.trim()).toBe('');
		});

		it('should handle sorting with empty values', fakeAsync(() => {
			// Setup with records containing empty values
			component.records = [...mockRecords, ...mockIncompleteRecords];
			component.ngOnChanges({ records: new SimpleChange(null, component.records, true) });
			fixture.detectChanges();

			// Spy on the sort change event
			spyOn(component.sortChange, 'emit');

			// Trigger sort on a column with empty values (shipper)
			const sortEvent: Sort = { active: 'shipper', direction: 'asc' };
			component.onSortChange(sortEvent);
			tick();

			// Verify the event was emitted with the correct sort parameters
			expect(component.sortChange.emit).toHaveBeenCalledWith(sortEvent);
		}));
	});
});
