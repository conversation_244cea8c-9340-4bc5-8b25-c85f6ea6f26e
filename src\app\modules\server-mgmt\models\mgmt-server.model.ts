export interface OrgInfo {
	id: string;
	idStr: string;
	companyName: string;
	partyRole: string;
	countryCode: string;
	locationName: string;
	regionCode: string;
	textualPostCode: string;
	cityCode: string;
	iataCargoAgentCode: string;
	airlineCode: string;
	airlinePrefix?: string;
	persons: Person[];
	server: Server;
	orgProperties: OrgProperties;

}

export interface Person {
	contactName: string;
	contactRole: string;
	jobTitle: string;
	employeeId: string;
	emailAddress: string;
	phoneNumber: string;
	contactDetailType?: string;
	textualValue?: string;
	contactDetailType1?: string;
	textualValue1?: string;
}

export interface Server {
	id: string;
	hasDataHolder: string;
	hasServerEndpoint: string;
	hasSupportedContentType: string[];
	hasSupportedLanguage: string;
}

export interface Id {
	iri: boolean;
	namespace: string;
	localName: string;
	resource: boolean;
	triple: boolean;
	bnode: boolean;
	literal: boolean;
}

export type NullableId = Id | null;

export interface ContactDetail {
	contactDetailType: string;
	textualValue: string;
}

export interface Person1 {
	lastName: string;
	contactRole: string;
	contactDetails: ContactDetail[];
	jobTitle?: string | null;
	employeeId?: string | null;
}

export interface BasedAtLocation {
	streetAddressLines: string;
	cityCode: string;
	regionCode: string;
	country: string;
	textualPostCode?: string | null;
}

export interface Organization {
	name: string;
	basedAtLocation: BasedAtLocation;
}

export interface OrgProperties {
	graphDbUrl?: string;
	userName?: string;
	password?: string;
	neOneUrl: string;
	keycloakUrl: string;
	grantType: string;
	clientId: string;
	clientSecret: string;
	logisticsAgentUri?: string;
}

export interface Party {
	partyRole: string;
}

export interface PublicAuthority {
	name: string;
	basedAtLocation: BasedAtLocation;
}

export interface Carrier {
	name: string;
	airlineCode: string;
	prefix: string;
	basedAtLocation: BasedAtLocation;
}

export interface Company {
	name: string;
	iataCargoAgentCode: string;
	basedAtLocation: BasedAtLocation;
}

export interface PostOrgInfo {
	id?: string | null;
	isAdd:string;
	orgStatus: string;
	orgServerType: string;
	organization: Organization;
	orgProperties: OrgProperties;
	party: Party;
	persons: Person1[];
	orgType: string;
	name: string;
	publicAuthority?: PublicAuthority | null;
	carrier?: Carrier | null;
	company?: Company | null;
	prefix?: string | null;
	idStr: string;
}

export enum ServerType {
	RESIDENT = 'Internal',
	EXTERNAL = 'External',
}

export interface PermissionResponse {
	permissionId: string;
	permissionCode: string;
	permissionName: string;
	path: string;
	icon: string;
	moduleName: string;
	resourceType: string;
	createTime: string;
}

export interface PermissionPost {
	permissionId: string;
	orgId: string;
	userType: string;
}
export interface PermissionPostObj {
	permissions: PermissionPost[];
}
