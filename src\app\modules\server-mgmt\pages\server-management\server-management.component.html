<div class="orll-server-management row">
	<div>
		<ul>
			@for (role of organizations; track role.id) {
				<li class="iata-main-header__secondary-nav__dropdown-list__item"
					[class.selected]="selectedItem?.id === role.id" (keydown.enter)="$event.stopPropagation()"
					(click)="onItemClick(role)">
					<a>
						<span class="role-name">{{ role.name }}</span>
					</a>
				</li>
			}
			<li class="iata-main-header__secondary-nav__dropdown-list__item"
				[class.selected]="selectedItem?.id === 'new-server'" (click)="newServer()"
				(keydown.enter)="$event.stopPropagation()">
				<div class="add-server-wrapper">
					<span>
						<mat-icon>add</mat-icon>
					</span>
					<span>
						@if (currentServerType === serverType.RESIDENT) {
							{{ 'system.server.button.addNewORLLResident' | translate }}
						} @else {
							{{ 'system.server.button.addNewExternalServer' | translate }}
						}
					</span>
				</div>
			</li>
		</ul>
	</div>

	<div class="iata-box">
		<mat-tab-group class="custom-tab-group" [mat-stretch-tabs]="false" (selectedTabChange)="onTabChanged($event)">
			<mat-tab label="{{ 'system.server.tab.oRLLResident' | translate }}">
				@if (currentServerType === serverType.RESIDENT) {
					<orll-server-detail [role]="selectedItem" (refresh)="refreshList($event)"></orll-server-detail>
				}
			</mat-tab>
			<mat-tab label="{{ 'system.server.tab.externalServers' | translate }}">
				@if (currentServerType === serverType.EXTERNAL) {
					<orll-server-detail [role]="selectedItem" [type]="serverType.EXTERNAL"
						(refresh)="refreshList($event)"></orll-server-detail>
				}
			</mat-tab>
		</mat-tab-group>
	</div>
</div>
