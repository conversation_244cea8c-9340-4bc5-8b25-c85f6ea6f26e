import { ComponentFixture, fakeAsync, TestBed, tick } from '@angular/core/testing';
import { MatSelectChange } from '@angular/material/select';
import { CodeName } from '@shared/models/code-name.model';
import { of } from 'rxjs';
import { Country } from 'src/app/modules/sli-mgmt/models/country.model';
import { Province } from 'src/app/modules/sli-mgmt/models/province.model';
import { ServerManagementService } from '../../services/server-management.service';
import { SliCreateRequestService } from 'src/app/modules/sli-mgmt/services/sli-create-request.service';
import { OrgInfo, ServerType } from '../../models/mgmt-server.model';
import { TranslateModule } from '@ngx-translate/core';
import { OrllServerDetailComponent } from './orll-server-detail.component';
import { OrgMgmtRequestService } from '@shared/services/org-mgmt-request.service';
import { CommonService } from '@shared/services/common.service';
import { FormBuilder } from '@angular/forms';
import { EnumCodeTypeModel } from '@shared/components/enum-code-form-item/enum-code-type.model';

const orgInfo: OrgInfo = {
	id: '',
	idStr: '',
	companyName: '',
	partyRole: '',
	countryCode: '',
	locationName: '',
	regionCode: '',
	textualPostCode: '',
	cityCode: '',
	iataCargoAgentCode: '',
	airlineCode: '',
	persons: [],
	server: {
		id: '',
		hasDataHolder: '',
		hasServerEndpoint: '',
		hasSupportedContentType: [],
		hasSupportedLanguage: '',
	},
	orgProperties: {
		userName: '',
		password: '',
		graphDbUrl: '',
		neOneUrl: '',
		keycloakUrl: '',
		grantType: '',
		clientId: '',
		clientSecret: '',
		logisticsAgentUri: '',
	},
};

describe('OrllResidentsDetailComponent', () => {
	let component: OrllServerDetailComponent;
	let fixture: ComponentFixture<OrllServerDetailComponent>;
	let mockCreateReqService: jasmine.SpyObj<SliCreateRequestService>;
	let mockMgmtService: jasmine.SpyObj<ServerManagementService>;
	let mockOrgService: jasmine.SpyObj<OrgMgmtRequestService>;
	let mockCommonService: jasmine.SpyObj<CommonService>;
	let fb: FormBuilder;

	beforeEach(async () => {
		mockCreateReqService = jasmine.createSpyObj('SliCreateRequestService', ['getProvinces', 'getCountries', 'getCities']);
		mockCreateReqService.getCountries.and.returnValue(of([]));
		mockCreateReqService.getProvinces.and.returnValue(of([]));
		mockMgmtService = jasmine.createSpyObj('ServerManagementService', ['getOrgInfo']);
		mockMgmtService.getOrgInfo.and.returnValue(of(orgInfo));

		mockOrgService = jasmine.createSpyObj('OrgMgmtRequestService', ['getEnumCode']);
		mockOrgService.getEnumCode.and.returnValue(of([]));
		mockCommonService = jasmine.createSpyObj('CommonService', ['showFormInvalid']);

		await TestBed.configureTestingModule({
			imports: [OrllServerDetailComponent, TranslateModule.forRoot()],
			providers: [
				FormBuilder,
				{ provide: SliCreateRequestService, useValue: mockCreateReqService },
				{ provide: ServerManagementService, useValue: mockMgmtService },
				{ provide: OrgMgmtRequestService, useValue: mockOrgService },
				{ provide: CommonService, useValue: mockCommonService },
			],
		}).compileComponents();

		fixture = TestBed.createComponent(OrllServerDetailComponent);
		component = fixture.componentInstance;
		fb = TestBed.inject(FormBuilder);

		(component as any).contactForm = fb.group({
			contactList: fb.array([]),
		});
		fixture.detectChanges();
	});

	it('should create', () => {
		expect(component).toBeTruthy();
	});

	describe('setupCountryValueChange()', () => {
		it('should fetch provinces data based on country code', fakeAsync(() => {
			const MOCK_COUNTRIES: Country[] = [
				{ code: 'US', name: 'United States', provinces: [] },
				{ code: 'CA', name: 'Canada', provinces: [] },
				{ code: 'UK', name: 'United Kingdom', provinces: [] },
			];

			const MOCK_PROVINCES: Province[] = [
				{ code: 'NY', name: 'New York', cities: [] },
				{ code: 'CA', name: 'California', cities: [] },
				{ code: 'TX', name: 'Texas', cities: [] },
			];

			component.countries = MOCK_COUNTRIES;
			mockCreateReqService.getProvinces.and.returnValue(of(MOCK_PROVINCES));

			(component as any).setupCountryValueChange('CA');

			expect(mockCreateReqService.getProvinces).toHaveBeenCalledWith(MOCK_COUNTRIES[1]);

			tick();

			expect(component.provinces).toEqual(MOCK_PROVINCES);
			expect(component.filteredProvinces).toEqual(MOCK_PROVINCES);
		}));
	});

	describe('setupRegionValueChange()', () => {
		it('should fetch cities data based on province code', fakeAsync(() => {
			const mockProvinces: Province[] = [
				{
					code: 'CA',
					name: 'California',
					cities: [],
				},
				{
					code: 'NY',
					name: 'New York',
					cities: [],
				},
			];
			const mockCities: CodeName[] = [
				{ code: 'LA', name: 'Los Angeles' },
				{ code: 'SF', name: 'San Francisco' },
			];

			component.provinces = mockProvinces;
			mockCreateReqService.getCities.and.returnValue(of(mockCities));

			(component as any).setupRegionValueChange('NY');

			expect(mockCreateReqService.getCities).toHaveBeenCalledWith(mockProvinces[1]);

			tick();

			expect(component.cities).toEqual(mockCities);
			expect(component.filteredCities).toEqual(mockCities);
		}));
	});

	describe('countryValueChange()', () => {
		it('should reset province and city fields and call setupCountryValueChange', () => {
			const setupCountrySpy = spyOn(component as any, 'setupCountryValueChange');

			const mockEvent = { value: 'US' } as MatSelectChange;

			component.countryValueChange(mockEvent);

			expect(component.form.get('province')?.value).toBe('');
			expect(component.form.get('city')?.value).toBe('');

			expect(setupCountrySpy).toHaveBeenCalledWith('US');
		});

		it('should use empty string when event is undefined', () => {
			const setupCountrySpy = spyOn(component as any, 'setupCountryValueChange');

			component.countryValueChange();

			expect(setupCountrySpy).toHaveBeenCalledWith('');
		});
	});

	describe('regionValueChange()', () => {
		it('should call setupRegionValueChange with event value', () => {
			const setupRegionSpy = spyOn(component as any, 'setupRegionValueChange');

			const mockEvent = { value: 'CA' } as MatSelectChange;

			component.regionValueChange(mockEvent);

			expect(setupRegionSpy).toHaveBeenCalledWith('CA');
		});

		it('should use empty string when event is undefined', () => {
			const setupRegionSpy = spyOn(component as any, 'setupRegionValueChange');

			component.regionValueChange();

			expect(setupRegionSpy).toHaveBeenCalledWith('');
		});
	});

	describe('initRefData()', () => {
		it('should initialize country data', fakeAsync(() => {
			const mockCountries: Country[] = [
				{
					code: 'US',
					name: 'United States',
					provinces: [],
				},
				{
					code: 'CN',
					name: 'China',
					provinces: [],
				},
			];

			mockCreateReqService.getCountries.and.returnValue(of(mockCountries));

			(component as any).initRefData();

			expect(mockCreateReqService.getCountries).toHaveBeenCalled();

			tick();

			expect(component.countries).toEqual(mockCountries);
			expect(component.filteredCountries).toEqual(mockCountries);
		}));
	});

	describe('Complete Flow Test', () => {
		it('should complete country->province->city selection flow', fakeAsync(() => {
			const mockCountries: Country[] = [
				{
					code: 'CN',
					name: 'China',
					provinces: [],
				},
			];
			const mockProvinces: Province[] = [
				{
					code: 'BJ',
					name: 'Beijing',
					cities: [],
				},
			];
			const mockCities: CodeName[] = [{ code: 'BJ-C', name: 'Beijing City' }];

			mockCreateReqService.getCountries.and.returnValue(of(mockCountries));
			(component as any).initRefData();
			tick();

			mockCreateReqService.getProvinces.and.returnValue(of(mockProvinces));
			component.countryValueChange({ value: 'CN' } as MatSelectChange);
			tick();

			mockCreateReqService.getCities.and.returnValue(of(mockCities));
			component.regionValueChange({ value: 'BJ' } as MatSelectChange);
			tick();

			expect(component.countries).toEqual(mockCountries);
			expect(component.provinces).toEqual(mockProvinces);
			expect(component.cities).toEqual(mockCities);
		}));
	});

	describe('newContact()', () => {
		it('should add a new contact group to contactList', () => {
			expect(component.contactForm.controls.contactList.length).toBe(0);

			component.newContact();

			expect(component.contactForm.controls.contactList.length).toBe(1);

			const contactGroup = component.contactForm.controls.contactList.at(0);
			expect(contactGroup.get('contactUri')).toBeTruthy();
			expect(contactGroup.get('contactRole')).toBeTruthy();
			expect(contactGroup.get('jobTitle')).toBeTruthy();
			expect(contactGroup.get('contactName')).toBeTruthy();
			expect(contactGroup.get('contactDetailType')).toBeTruthy();
			expect(contactGroup.get('textualValue')).toBeTruthy();
			expect(contactGroup.get('contactDetailType1')).toBeTruthy();
			expect(contactGroup.get('textualValue1')).toBeTruthy();
		});

		it('should set default values for contactDetailType and contactDetailType1', () => {
			component.newContact();

			const contactGroup = component.contactForm.controls.contactList.at(0);
			expect(contactGroup.get('contactDetailType')?.value).toBe('PHONE_NUMBER');
			expect(contactGroup.get('contactDetailType1')?.value).toBe('EMAIL_ADDRESS');
		});

		it('should disable the entire contact group when type is EXTERNAL', () => {
			component.type = component.serverType.EXTERNAL;
			component.contactForm.controls.contactList.reset();
			component.newContact();

			const contactGroup = component.contactForm.controls.contactList.at(0);
			expect(contactGroup.controls.contactDetailType.disabled).toBeTrue();
			expect(contactGroup.controls.contactUri.disabled).toBeFalse();
		});

		it('should enable contactUri control when type is EXTERNAL', () => {
			component.type = component.serverType.EXTERNAL;

			component.newContact();

			const contactGroup = component.contactForm.controls.contactList.at(0);
			expect(contactGroup.get('contactUri')?.enabled).toBeTrue();
		});

		it('should not disable contact group when type is not EXTERNAL', () => {
			component.type = ServerType.RESIDENT; // or any other value

			component.newContact();

			const contactGroup = component.contactForm.controls.contactList.at(0);
			expect(contactGroup.disabled).toBeFalse();
		});
	});

	describe('deleteContact()', () => {
		beforeEach(() => {
			// Add some contacts first
			component.newContact();
			component.newContact();
			component.newContact();
		});

		it('should remove contact at specified index', () => {
			const initialLength = component.contactForm.controls.contactList.length;
			expect(initialLength).toBe(3);

			component.deleteContact(1);

			expect(component.contactForm.controls.contactList.length).toBe(2);
		});

		it('should remove the first contact when index is 0', () => {
			const firstContactBefore = component.contactForm.controls.contactList.at(0);

			component.deleteContact(0);

			const firstContactAfter = component.contactForm.controls.contactList.at(0);
			expect(firstContactAfter).not.toBe(firstContactBefore);
			expect(component.contactForm.controls.contactList.length).toBe(2);
		});

		it('should remove the last contact when index is last', () => {
			const lastIndex = component.contactForm.controls.contactList.length - 1;
			const lastContactBefore = component.contactForm.controls.contactList.at(lastIndex);

			component.deleteContact(lastIndex);

			const newLastIndex = component.contactForm.controls.contactList.length - 1;
			const lastContactAfter = component.contactForm.controls.contactList.at(newLastIndex);
			expect(lastContactAfter).not.toBe(lastContactBefore);
			expect(component.contactForm.controls.contactList.length).toBe(2);
		});

		it('should not throw error when deleting from empty list', () => {
			(component as any).contactForm = fb.group({
				contactList: fb.array([]),
			});

			expect(() => component.deleteContact(0)).not.toThrow();
			expect(component.contactForm.controls.contactList.length).toBe(0);
		});

		it('should not throw error when index is out of bounds', () => {
			expect(() => component.deleteContact(10)).not.toThrow();
			expect(component.contactForm.controls.contactList.length).toBe(3);
		});

		it('should not throw error when index is negative', () => {
			expect(() => component.deleteContact(-1)).not.toThrow();
			expect(component.contactForm.controls.contactList.length).toBe(2);
		});
	});

	describe('initRefData()', () => {
		const mockCountries: Country[] = [
			{
				code: 'US',
				name: 'United States',
				provinces: [],
			},
			{
				code: 'CN',
				name: 'China',
				provinces: [],
			},
		];

		const mockContactRoles: CodeName[] = [
			{ code: 'ADMIN', name: 'Administrator' },
			{ code: 'MANAGER', name: 'Manager' },
		];

		const mockContactDetailTypes: CodeName[] = [
			{ code: 'PHONE', name: 'Phone Number' },
			{ code: 'EMAIL', name: 'Email Address' },
		];

		it('should call getCountries and update countries and filteredCountries', fakeAsync(() => {
			mockCreateReqService.getCountries.and.returnValue(of(mockCountries));
			mockOrgService.getEnumCode.and.returnValue(of([]));

			(component as any).initRefData();
			tick();

			expect(mockCreateReqService.getCountries).toHaveBeenCalled();
			expect(component.countries).toEqual(mockCountries);
			expect(component.filteredCountries).toEqual(mockCountries);
		}));

		it('should call getEnumCode for CONTACT_ROLE and update contactRoles', fakeAsync(() => {
			mockCreateReqService.getCountries.and.returnValue(of([]));
			mockOrgService.getEnumCode.withArgs(EnumCodeTypeModel.CONTACT_ROLE).and.returnValue(of(mockContactRoles));
			mockOrgService.getEnumCode.withArgs(EnumCodeTypeModel.CONTACT_DETAIL_TYPE).and.returnValue(of([]));

			(component as any).initRefData();
			tick();

			expect(mockOrgService.getEnumCode).toHaveBeenCalledWith(EnumCodeTypeModel.CONTACT_ROLE);
			expect(component.contactRoles).toEqual(mockContactRoles);
		}));

		it('should call getEnumCode for CONTACT_DETAIL_TYPE and update contactDetailTypes', fakeAsync(() => {
			mockCreateReqService.getCountries.and.returnValue(of([]));
			mockOrgService.getEnumCode.withArgs(EnumCodeTypeModel.CONTACT_ROLE).and.returnValue(of([]));
			mockOrgService.getEnumCode.withArgs(EnumCodeTypeModel.CONTACT_DETAIL_TYPE).and.returnValue(of(mockContactDetailTypes));

			(component as any).initRefData();
			tick();

			expect(mockOrgService.getEnumCode).toHaveBeenCalledWith(EnumCodeTypeModel.CONTACT_DETAIL_TYPE);
			expect(component.contactDetailTypes).toEqual(mockContactDetailTypes);
		}));

		it('should handle all service calls simultaneously', fakeAsync(() => {
			mockCreateReqService.getCountries.and.returnValue(of(mockCountries));
			mockOrgService.getEnumCode.withArgs(EnumCodeTypeModel.CONTACT_ROLE).and.returnValue(of(mockContactRoles));
			mockOrgService.getEnumCode.withArgs(EnumCodeTypeModel.CONTACT_DETAIL_TYPE).and.returnValue(of(mockContactDetailTypes));

			(component as any).initRefData();
			tick();

			expect(mockCreateReqService.getCountries).toHaveBeenCalled();
			expect(mockOrgService.getEnumCode).toHaveBeenCalledWith(EnumCodeTypeModel.CONTACT_ROLE);
			expect(mockOrgService.getEnumCode).toHaveBeenCalledWith(EnumCodeTypeModel.CONTACT_DETAIL_TYPE);

			expect(component.countries).toEqual(mockCountries);
			expect(component.filteredCountries).toEqual(mockCountries);
			expect(component.contactRoles).toEqual(mockContactRoles);
			expect(component.contactDetailTypes).toEqual(mockContactDetailTypes);
		}));

		it('should handle empty responses from services', fakeAsync(() => {
			mockCreateReqService.getCountries.and.returnValue(of([]));
			mockOrgService.getEnumCode.withArgs(EnumCodeTypeModel.CONTACT_ROLE).and.returnValue(of([]));
			mockOrgService.getEnumCode.withArgs(EnumCodeTypeModel.CONTACT_DETAIL_TYPE).and.returnValue(of([]));

			(component as any).initRefData();
			tick();

			expect(component.countries).toEqual([]);
			expect(component.filteredCountries).toEqual([]);
			expect(component.contactRoles).toEqual([]);
			expect(component.contactDetailTypes).toEqual([]);
		}));

		it('should maintain separate arrays for different data types', fakeAsync(() => {
			const differentMockContactRoles: CodeName[] = [{ code: 'DIRECTOR', name: 'Director' }];

			mockCreateReqService.getCountries.and.returnValue(of(mockCountries));
			mockOrgService.getEnumCode.withArgs(EnumCodeTypeModel.CONTACT_ROLE).and.returnValue(of(differentMockContactRoles));
			mockOrgService.getEnumCode.withArgs(EnumCodeTypeModel.CONTACT_DETAIL_TYPE).and.returnValue(of(mockContactDetailTypes));

			(component as any).initRefData();
			tick();

			expect(component.countries).toEqual(mockCountries);
			expect(component.contactRoles).toEqual(differentMockContactRoles);
			expect(component.contactDetailTypes).toEqual(mockContactDetailTypes);
		}));

		it('should call services only once when method is called multiple times', fakeAsync(() => {
			mockCreateReqService.getCountries.and.returnValue(of(mockCountries));
			mockOrgService.getEnumCode.and.returnValue(of([]));

			expect(mockCreateReqService.getCountries).toHaveBeenCalledTimes(1);
			expect(mockOrgService.getEnumCode).toHaveBeenCalledTimes(2);
		}));
	});
});
