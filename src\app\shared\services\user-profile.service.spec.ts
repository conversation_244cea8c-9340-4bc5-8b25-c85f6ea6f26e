import { HttpTestingController, provideHttpClientTesting } from '@angular/common/http/testing';
import { TestBed } from '@angular/core/testing';
import { environment } from '@environments/environment';
import { UserProfileService } from '@shared/services/user-profile.service';
import { UserProfile } from '@shared/models/user-profile.model';
import { UserRole, UserPermission, Modules } from '@shared/models/user-role.model';
import { Observable, of } from 'rxjs';
import { provideHttpClient, withInterceptorsFromDi } from '@angular/common/http';

export class UserProfileServiceMock {
	getProfile(): Observable<UserProfile> {
		return of({} as UserProfile);
	}
}

// Helper function to create complete UserProfile objects for testing
function createMockUserProfile(overrides: Partial<UserProfile> = {}): UserProfile {
	return {
		userId: 'test-user-id',
		email: '<EMAIL>',
		firstName: 'Test',
		lastName: 'User',
		primaryOrgId: 'primary-org',
		primaryOrgName: 'Primary Org',
		orgId: 'org-id',
		orgName: 'Org Name',
		orgType: 'ORG_TYPE',
		userType: '1',
		menuList: [],
		permissionList: [],
		orgList: [],
		...overrides,
	};
}

describe('UserProfileService', () => {
	let service: UserProfileService;
	let httpController: HttpTestingController;

	beforeEach(() => {
		TestBed.configureTestingModule({
			imports: [],
			providers: [UserProfileService, provideHttpClient(withInterceptorsFromDi()), provideHttpClientTesting()],
		});
		service = TestBed.inject(UserProfileService);
		httpController = TestBed.inject(HttpTestingController);
	});

	it('#getProfile should retrieve the user profile', (done: DoneFn) => {
		const userStub = { firstName: 'John', lastName: 'Doe' } as UserProfile;
		service.getProfile().subscribe((user: UserProfile) => {
			expect(user).toEqual(userStub);
			expect(service['profile']).toEqual(userStub);
			done();
		});

		const req = httpController.expectOne({
			method: 'GET',
			url: `${environment.baseApi}/user/user/current`,
		});

		req.flush(userStub);
	});

	it('#getProfile might return the cached user profile', (done: DoneFn) => {
		const userStub = { firstName: 'John', lastName: 'Doe' } as UserProfile;
		service['profile'] = userStub;

		service.getProfile().subscribe((user: UserProfile) => {
			expect(user).toEqual(userStub);
			done();
		});
	});

	it('#currentUser should return the cached user profile', () => {
		const userStub = { firstName: 'John', lastName: 'Doe' } as UserProfile;
		service['profile'] = userStub;
		const user = service.currentUser;
		expect(user).toEqual(userStub);
	});

	it('#currentUser might return null if user profile was not retrieved yet', () => {
		expect(service.currentUser).toBeNull();
	});

	describe('#getProfile with forceReload', () => {
		it('should force reload profile when forceReload is true', (done: DoneFn) => {
			const cachedUser = { firstName: 'Cached', lastName: 'User' } as UserProfile;
			const newUser = { firstName: 'New', lastName: 'User' } as UserProfile;

			// Set cached profile
			service['profile'] = cachedUser;

			service.getProfile(true).subscribe((user: UserProfile) => {
				expect(user).toEqual(newUser);
				expect(service['profile']).toEqual(newUser);
				done();
			});

			const req = httpController.expectOne({
				method: 'GET',
				url: `${environment.baseApi}/user/user/current`,
			});

			req.flush(newUser);
		});

		it('should return cached profile when forceReload is false and profile exists', (done: DoneFn) => {
			const cachedUser = { firstName: 'Cached', lastName: 'User' } as UserProfile;
			service['profile'] = cachedUser;

			service.getProfile(false).subscribe((user: UserProfile) => {
				expect(user).toEqual(cachedUser);
				done();
			});

			// Should not make HTTP request
			httpController.expectNone(`${environment.baseApi}/user/user/current`);
		});
	});

	describe('#currentUser$', () => {
		it('should return observable of current user profile', (done: DoneFn) => {
			const userStub = { firstName: 'John', lastName: 'Doe' } as UserProfile;

			service.currentUser$.subscribe((user: UserProfile | null) => {
				if (user) {
					expect(user).toEqual(userStub);
					done();
				}
			});

			// Trigger profile update
			service['profile'] = userStub;
			service['profile$'].next(userStub);
		});

		it('should initially return null', (done: DoneFn) => {
			service.currentUser$.subscribe((user: UserProfile | null) => {
				expect(user).toBeNull();
				done();
			});
		});
	});

	describe('#changeRole', () => {
		it('should change user organization role', (done: DoneFn) => {
			const orgId = 'new-org-123';
			const expectedResponse = true;

			service.changeRole(orgId).subscribe((result: boolean) => {
				expect(result).toBe(expectedResponse);
				done();
			});

			const req = httpController.expectOne({
				method: 'POST',
				url: `${environment.baseApi}/user/user/change-org`,
			});

			expect(req.request.body).toEqual({ orgId });
			req.flush(expectedResponse);
		});

		it('should handle change role failure', (done: DoneFn) => {
			const orgId = 'invalid-org';

			service.changeRole(orgId).subscribe({
				next: () => done.fail('Should have failed'),
				error: (error) => {
					expect(error).toBeDefined();
					done();
				},
			});

			const req = httpController.expectOne({
				method: 'POST',
				url: `${environment.baseApi}/user/user/change-org`,
			});

			req.error(new ProgressEvent('Network error'));
		});
	});

	describe('#isSuperUser', () => {
		it('should return true when user is super user', () => {
			const superUser = {
				firstName: 'Super',
				lastName: 'User',
				userType: UserRole.SUPER_USER,
			} as UserProfile;
			service['profile'] = superUser;

			const result = service.isSuperUser();
			expect(result).toBe(true);
		});

		it('should return false when user is not super user', () => {
			const normalUser = {
				firstName: 'Normal',
				lastName: 'User',
				userType: '1',
			} as UserProfile;
			service['profile'] = normalUser;

			const result = service.isSuperUser();
			expect(result).toBe(false);
		});

		it('should return false when no user profile exists', () => {
			service['profile'] = null;

			const result = service.isSuperUser();
			expect(result).toBe(false);
		});
	});

	describe('#hasSomeRole', () => {
		it('should return true when user has one of the needed roles', (done: DoneFn) => {
			const userWithRole = createMockUserProfile({
				orgType: UserRole.SHIPPER,
			});

			service.hasSomeRole([UserRole.SHIPPER, UserRole.FORWARDER]).subscribe((hasRole: boolean) => {
				expect(hasRole).toBe(true);
				done();
			});

			const req = httpController.expectOne({
				method: 'GET',
				url: `${environment.baseApi}/user/user/current`,
			});

			req.flush(userWithRole);
		});

		it('should return false when user does not have any of the needed roles', (done: DoneFn) => {
			const userWithoutRole = createMockUserProfile({
				orgType: UserRole.CARRIER,
			});

			service.hasSomeRole([UserRole.SHIPPER, UserRole.FORWARDER]).subscribe((hasRole: boolean) => {
				expect(hasRole).toBe(false);
				done();
			});

			const req = httpController.expectOne({
				method: 'GET',
				url: `${environment.baseApi}/user/user/current`,
			});

			req.flush(userWithoutRole);
		});

		it('should return false when needed roles array is empty', (done: DoneFn) => {
			const userStub = createMockUserProfile({
				orgType: UserRole.SHIPPER,
			});

			service.hasSomeRole([]).subscribe((hasRole: boolean) => {
				expect(hasRole).toBe(false);
				done();
			});

			const req = httpController.expectOne({
				method: 'GET',
				url: `${environment.baseApi}/user/user/current`,
			});

			req.flush(userStub);
		});
	});

	describe('#hasPermission', () => {
		it('should return true when user has the required permission for the module', (done: DoneFn) => {
			const userWithPermission = createMockUserProfile({
				permissionList: [
					{
						name: 'SLI Management',
						module: Modules.SLI,
						code: [UserPermission.CREATE, UserPermission.UPDATE],
					},
				],
			});

			service.hasPermission(UserPermission.CREATE, Modules.SLI).subscribe((hasPermission: boolean) => {
				expect(hasPermission).toBe(true);
				done();
			});

			const req = httpController.expectOne({
				method: 'GET',
				url: `${environment.baseApi}/user/user/current`,
			});

			req.flush(userWithPermission);
		});

		it('should return false when user does not have the required permission for the module', (done: DoneFn) => {
			const userWithoutPermission = createMockUserProfile({
				permissionList: [
					{
						name: 'SLI Management',
						module: Modules.SLI,
						code: [UserPermission.QUERY],
					},
				],
			});

			service.hasPermission(UserPermission.DELETE, Modules.SLI).subscribe((hasPermission: boolean) => {
				expect(hasPermission).toBe(false);
				done();
			});

			const req = httpController.expectOne({
				method: 'GET',
				url: `${environment.baseApi}/user/user/current`,
			});

			req.flush(userWithoutPermission);
		});

		it('should return false when user has permission for different module', (done: DoneFn) => {
			const userWithDifferentModulePermission = createMockUserProfile({
				permissionList: [
					{
						name: 'HAWB Management',
						module: Modules.HAWB,
						code: [UserPermission.CREATE, UserPermission.UPDATE],
					},
				],
			});

			service.hasPermission(UserPermission.CREATE, Modules.SLI).subscribe((hasPermission: boolean) => {
				expect(hasPermission).toBe(false);
				done();
			});

			const req = httpController.expectOne({
				method: 'GET',
				url: `${environment.baseApi}/user/user/current`,
			});

			req.flush(userWithDifferentModulePermission);
		});

		it('should return false when user has no permissions', (done: DoneFn) => {
			const userWithNoPermissions = createMockUserProfile({
				permissionList: [],
			});

			service.hasPermission(UserPermission.CREATE, Modules.SLI).subscribe((hasPermission: boolean) => {
				expect(hasPermission).toBe(false);
				done();
			});

			const req = httpController.expectOne({
				method: 'GET',
				url: `${environment.baseApi}/user/user/current`,
			});

			req.flush(userWithNoPermissions);
		});
	});

	describe('Error Handling', () => {
		it('should handle HTTP errors when getting profile', (done: DoneFn) => {
			service.getProfile().subscribe({
				next: () => done.fail('Should have failed'),
				error: (error) => {
					expect(error).toBeDefined();
					done();
				},
			});

			const req = httpController.expectOne({
				method: 'GET',
				url: `${environment.baseApi}/user/user/current`,
			});

			req.error(new ProgressEvent('Network error'));
		});

		it('should handle HTTP errors in hasSomeRole', (done: DoneFn) => {
			service.hasSomeRole([UserRole.SHIPPER]).subscribe({
				next: () => done.fail('Should have failed'),
				error: (error) => {
					expect(error).toBeDefined();
					done();
				},
			});

			const req = httpController.expectOne({
				method: 'GET',
				url: `${environment.baseApi}/user/user/current`,
			});

			req.error(new ProgressEvent('Network error'));
		});

		it('should handle HTTP errors in hasPermission', (done: DoneFn) => {
			service.hasPermission(UserPermission.CREATE, Modules.SLI).subscribe({
				next: () => done.fail('Should have failed'),
				error: (error) => {
					expect(error).toBeDefined();
					done();
				},
			});

			const req = httpController.expectOne({
				method: 'GET',
				url: `${environment.baseApi}/user/user/current`,
			});

			req.error(new ProgressEvent('Network error'));
		});
	});

	describe('BehaviorSubject Updates', () => {
		it('should update profile$ when getProfile is called', (done: DoneFn) => {
			const userStub = { firstName: 'John', lastName: 'Doe' } as UserProfile;
			let callCount = 0;

			service.currentUser$.subscribe((user: UserProfile | null) => {
				callCount++;
				if (callCount === 1) {
					expect(user).toBeNull(); // Initial value
				} else if (callCount === 2) {
					expect(user).toEqual(userStub); // Updated value
					done();
				}
			});

			service.getProfile().subscribe();

			const req = httpController.expectOne({
				method: 'GET',
				url: `${environment.baseApi}/user/user/current`,
			});

			req.flush(userStub);
		});

		it('should not update profile$ when using cached profile', (done: DoneFn) => {
			const cachedUser = { firstName: 'Cached', lastName: 'User' } as UserProfile;
			service['profile'] = cachedUser;
			service['profile$'].next(cachedUser);

			let callCount = 0;
			const subscription = service.currentUser$.subscribe((user: UserProfile | null) => {
				callCount++;
				expect(user).toEqual(cachedUser);
			});

			service.getProfile(false).subscribe(() => {
				// Verify that profile$ was not updated again
				expect(callCount).toBe(1);
				subscription.unsubscribe();
				done();
			});

			httpController.expectNone(`${environment.baseApi}/user/user/current`);
		});
	});

	describe('Service Initialization', () => {
		it('should initialize with null profile', () => {
			expect(service.currentUser).toBeNull();
		});

		it('should initialize with null profile$ value', (done: DoneFn) => {
			service.currentUser$.subscribe((user: UserProfile | null) => {
				expect(user).toBeNull();
				done();
			});
		});
	});

	afterEach(() => {
		httpController.verify();
	});
});
