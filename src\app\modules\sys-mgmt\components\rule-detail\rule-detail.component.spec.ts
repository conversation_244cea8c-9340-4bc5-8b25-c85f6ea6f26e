import { ComponentFixture, TestBed } from '@angular/core/testing';
import { RuleDetailComponent } from './rule-detail.component';
import { RuleService } from '../../services/rule.service';
import { OrgMgmtRequestService } from '@shared/services/org-mgmt-request.service';
import { FormBuilder } from '@angular/forms';
import { MatDialog, MatDialogRef, MAT_DIALOG_DATA, MatDialogModule } from '@angular/material/dialog';
import { RuleDetailObj, RuleListObj } from '../../models/rule.model';
import { of, throwError } from 'rxjs';
import { Organization } from '@shared/models/organization.model';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { UserProfileService } from '@shared/services/user-profile.service';
import { UserProfile } from '@shared/models/user-profile.model';
import { NO_ERRORS_SCHEMA } from '@angular/core';
import { NoopAnimationsModule } from '@angular/platform-browser/animations';

const mockRule: RuleDetailObj = {
	id: '123',
	holder: ['org1'],
	requestType: 'type1',
	action: 'allow',
	request: ['org2'],
};

const mockRuleListObj: RuleListObj = {
	id: '111',
	holder: '222',
	requestType: '22',
	request: '',
	action: '',
	holderNames: '',
	requestTypeDescription: '',
	requestNames: '',
	actionDescription: '',
};

const orgs: Organization[] = [
	{
		id: '111',
		name: '111',
		orgType: '11',
	},
	{
		id: '222',
		name: '222',
		orgType: '222',
	},
];

const mockUserProfile: UserProfile = {
	userId: 'user123',
	orgId: 'org456',
	primaryOrgId: 'org456',
	primaryOrgName: 'Test Organization',
	firstName: 'Test',
	lastName: 'User',
	email: '<EMAIL>',
	orgName: 'Test Organization',
	orgType: 'CARRIER',
	userType: 'INTERNAL',
	menuList: [],
	permissionList: [],
	orgList: [],
};

describe('RuleDetailComponent', () => {
	let component: RuleDetailComponent;
	let fixture: ComponentFixture<RuleDetailComponent>;
	let ruleService: jasmine.SpyObj<RuleService>;
	let orgService: jasmine.SpyObj<OrgMgmtRequestService>;
	let dialogRef: jasmine.SpyObj<MatDialogRef<RuleDetailComponent>>;
	let dialog: jasmine.SpyObj<MatDialog>;
	let translateService: jasmine.SpyObj<TranslateService>;
	let userProfileService: jasmine.SpyObj<UserProfileService>;

	beforeEach(async () => {
		ruleService = jasmine.createSpyObj('RuleService', ['deleteRule', 'saveRule', 'getRule']);
		orgService = jasmine.createSpyObj('OrgMgmtRequestService', ['getOrgList']);
		dialogRef = jasmine.createSpyObj<MatDialogRef<RuleDetailComponent>>('MatDialogRef', ['close']);
		dialog = jasmine.createSpyObj('MatDialog', ['open']);
		const mockDialogRef = {
			afterClosed: jasmine.createSpy('afterClosed').and.returnValue(of(true)),
			close: jasmine.createSpy('close'),
		};
		dialog.open.and.returnValue(mockDialogRef as any);
		translateService = {
			instant: jasmine.createSpy('instant').and.returnValue('Translated Text'),
			get: jasmine.createSpy('get').and.returnValue(of('Translated Text')),
			onLangChange: of({}),
			onTranslationChange: of({}),
			onDefaultLangChange: of({}),
		} as any;
		userProfileService = jasmine.createSpyObj('UserProfileService', ['getProfile'], {
			currentUser$: of(mockUserProfile),
		});

		// Set up default return values
		orgService.getOrgList.and.returnValue(of(orgs));

		await TestBed.configureTestingModule({
			imports: [RuleDetailComponent, TranslateModule.forRoot(), MatDialogModule, NoopAnimationsModule],
			providers: [
				FormBuilder,
				{ provide: RuleService, useValue: ruleService },
				{ provide: OrgMgmtRequestService, useValue: orgService },
				{ provide: MatDialogRef, useValue: dialogRef },
				{ provide: MatDialog, useValue: dialog },
				{ provide: TranslateService, useValue: translateService },
				{ provide: UserProfileService, useValue: userProfileService },
				{ provide: MAT_DIALOG_DATA, useValue: {} },
			],
			schemas: [NO_ERRORS_SCHEMA],
		}).compileComponents();

		fixture = TestBed.createComponent(RuleDetailComponent);
		component = fixture.componentInstance;
		component.dataLoading = false;
		component.isSave = false;
		component.isEdit = false;
		fixture.detectChanges();
	});

	it('should create', () => {
		expect(component).toBeTruthy();
	});

	describe('ngOnInit', () => {
		it('should initialize component with user profile and organization list', () => {
			component.ngOnInit();

			expect(component.holderId).toBe('org456');
			expect(component.requestList).toEqual(orgs);
			expect(orgService.getOrgList).toHaveBeenCalled();
		});

		it('should load rule data and patch form when data.id exists', () => {
			const ruleData = { ...mockRule, requestTypeDescription: 'Description' };
			ruleService.getRule.and.returnValue(of(ruleData));
			component.data = mockRuleListObj;

			component.ngOnInit();
			fixture.detectChanges();

			expect(ruleService.getRule).toHaveBeenCalledWith(mockRuleListObj);

			ruleService.getRule.calls.mostRecent().returnValue.subscribe();
			expect(component.ruleForm.value.requestType).toBe('type1');
			expect(component.isEdit).toBeTrue();
			expect(component.dataLoading).toBeFalse();
		});

		it('should not call getRule if no data.id', () => {
			component.data = {
				holder: '',
				requestType: '',
				request: '',
				action: '',
				holderNames: '',
				requestTypeDescription: '',
				requestNames: '',
				actionDescription: '',
			};
			component.ngOnInit();

			expect(ruleService.getRule).not.toHaveBeenCalled();
		});

		it('should handle null user profile', () => {
			Object.defineProperty(userProfileService, 'currentUser$', {
				value: of(null),
				configurable: true,
			});

			component.ngOnInit();

			expect(component.holderId).toBe('');
		});

		it('should handle user profile without orgId', () => {
			const userWithoutOrgId = { ...mockUserProfile, orgId: undefined as any };
			Object.defineProperty(userProfileService, 'currentUser$', {
				value: of(userWithoutOrgId),
				configurable: true,
			});

			component.ngOnInit();

			expect(component.holderId).toBe('');
		});
	});

	describe('getFormData', () => {
		beforeEach(() => {
			component.holderId = 'org456';
		});

		it('should return form data with correct structure', () => {
			component.ruleForm.patchValue({
				requestType: 'type1',
				request: ['req1'],
				action: 'allow',
			});

			const result = component.getFormData();

			expect(result.holder).toEqual(['org456']);
			expect(result.requestType).toBe('type1');
			expect(result.action).toBe('allow');
			expect(result.request).toEqual(['req1']);
		});

		it('should include id when editing', () => {
			component.data = mockRuleListObj;
			component.ruleForm.patchValue(mockRule);

			const result = component.getFormData();

			expect(result.id).toBe('111');
		});

		it('should handle empty holderId', () => {
			component.holderId = '';
			component.ruleForm.patchValue({
				requestType: 'type1',
				request: ['req1'],
				action: 'allow',
			});

			const result = component.getFormData();

			expect(result.holder).toEqual([]);
		});

		it('should handle null form values', () => {
			component.ruleForm.patchValue({
				requestType: null,
				request: null,
				action: null,
			});

			const result = component.getFormData();

			expect(result.requestType).toBe('');
			expect(result.request).toEqual([]);
			expect(result.action).toBe('');
		});

		it('should handle undefined form values', () => {
			component.ruleForm.patchValue({
				requestType: undefined,
				request: undefined,
				action: undefined,
			});

			const result = component.getFormData();

			expect(result.requestType).toBe('');
			expect(result.request).toEqual([]);
			expect(result.action).toBe('');
		});

		it('should not include id when creating new rule', () => {
			component.data = {
				holder: '',
				requestType: '',
				request: '',
				action: '',
				holderNames: '',
				requestTypeDescription: '',
				requestNames: '',
				actionDescription: '',
			};
			component.ruleForm.patchValue({
				requestType: 'type1',
				request: ['req1'],
				action: 'allow',
			});

			const result = component.getFormData();

			expect(result.id).toBeUndefined();
		});
	});

	describe('saveRule', () => {
		beforeEach(() => {
			component.holderId = 'org456';
			component.ruleForm.patchValue({
				requestType: 'type1',
				request: ['req1'],
				action: 'allow',
			});
		});

		it('should call saveRule with correct data and close dialog on success', () => {
			ruleService.saveRule.and.returnValue(of(''));
			component.saveRule();

			expect(component.isSave).toBeTrue();
			expect(component.dataLoading).toBe(false); // dataLoading is set to false after the operation completes
			expect(ruleService.saveRule).toHaveBeenCalledWith(
				jasmine.objectContaining({
					holder: ['org456'],
					requestType: 'type1',
					action: 'allow',
					request: ['req1'],
				})
			);

			ruleService.saveRule.calls.mostRecent().returnValue.subscribe();
			expect(component.dataLoading).toBe(false);
			expect(dialogRef.close).toHaveBeenCalledWith(true);
		});

		it('should handle form validation when form is invalid', () => {
			component.ruleForm.patchValue({
				requestType: '',
				request: ['req1'],
				action: '',
			});

			// Mock the dialog.open to avoid the actual dialog opening
			spyOn(component['dialog'], 'open').and.returnValue({
				afterClosed: () => of(false),
			} as any);

			component.saveRule();

			expect(component.isSave).toBeTrue();
			expect(component.ruleForm.touched).toBeTruthy();
			expect(component['dialog'].open).toHaveBeenCalled();
			expect(ruleService.saveRule).not.toHaveBeenCalled();
		});

		it('should handle save error', () => {
			ruleService.saveRule.and.returnValue(throwError(() => new Error('Save failed')));
			component.saveRule();

			expect(component.dataLoading).toBe(false);
			expect(dialogRef.close).not.toHaveBeenCalled();
		});

		it('should mark all form fields as touched when saving', () => {
			ruleService.saveRule.and.returnValue(of(''));
			spyOn(component.ruleForm, 'markAllAsTouched');

			component.saveRule();

			expect(component.ruleForm.markAllAsTouched).toHaveBeenCalled();
		});

		it('should set dataLoading to true when starting save operation', () => {
			ruleService.saveRule.and.returnValue(of(''));

			component.saveRule();

			expect(component.dataLoading).toBe(false); // Will be false after observable completes
		});

		it('should handle save operation with editing mode', () => {
			component.data = mockRuleListObj;
			component.isEdit = true;
			ruleService.saveRule.and.returnValue(of(''));

			component.saveRule();

			expect(ruleService.saveRule).toHaveBeenCalledWith(
				jasmine.objectContaining({
					id: '111',
					holder: ['org456'],
					requestType: 'type1',
					action: 'allow',
					request: ['req1'],
				})
			);
		});
	});

	describe('Form Validation', () => {
		it('should initialize form with correct structure', () => {
			expect(component.ruleForm.get('requestType')).toBeDefined();
			expect(component.ruleForm.get('request')).toBeDefined();
			expect(component.ruleForm.get('action')).toBeDefined();
		});

		it('should require requestType field', () => {
			const requestTypeControl = component.ruleForm.get('requestType');
			requestTypeControl?.setValue('');
			expect(requestTypeControl?.hasError('required')).toBeTruthy();
		});

		it('should require action field', () => {
			const actionControl = component.ruleForm.get('action');
			actionControl?.setValue('');
			expect(actionControl?.hasError('required')).toBeTruthy();
		});

		it('should not require request field', () => {
			const requestControl = component.ruleForm.get('request');
			requestControl?.setValue([]);
			expect(requestControl?.hasError('required')).toBeFalsy();
		});

		it('should accept valid requestType values', () => {
			const requestTypeControl = component.ruleForm.get('requestType');
			requestTypeControl?.setValue('1');
			expect(requestTypeControl?.valid).toBeTruthy();
		});

		it('should accept valid action values', () => {
			const actionControl = component.ruleForm.get('action');
			actionControl?.setValue('allow');
			expect(actionControl?.valid).toBeTruthy();
		});

		it('should accept array values for request field', () => {
			const requestControl = component.ruleForm.get('request');
			requestControl?.setValue(['org1', 'org2']);
			expect(requestControl?.valid).toBeTruthy();
		});

		it('should validate form as invalid when required fields are empty', () => {
			component.ruleForm.patchValue({
				requestType: '',
				request: [],
				action: '',
			});

			expect(component.ruleForm.invalid).toBeTruthy();
		});

		it('should validate form as valid when all required fields are filled', () => {
			component.ruleForm.patchValue({
				requestType: '1',
				request: ['org1'],
				action: 'allow',
			});

			expect(component.ruleForm.valid).toBeTruthy();
		});

		it('should handle form reset', () => {
			component.ruleForm.patchValue({
				requestType: '1',
				request: ['org1'],
				action: 'allow',
			});

			component.ruleForm.reset();

			expect(component.ruleForm.get('requestType')?.value).toBe(null);
			expect(component.ruleForm.get('request')?.value).toBe(null);
			expect(component.ruleForm.get('action')?.value).toBe(null);
		});
	});

	describe('Error Handling', () => {
		it('should handle getRule error', () => {
			ruleService.getRule.and.returnValue(throwError(() => new Error('Get rule failed')));
			component.data = mockRuleListObj;

			component.ngOnInit();

			expect(component.dataLoading).toBe(false);
			expect(component.isEdit).toBe(false);
		});

		it('should handle getRule network error', () => {
			ruleService.getRule.and.returnValue(throwError(() => ({ status: 500, message: 'Server error' })));
			component.data = mockRuleListObj;

			component.ngOnInit();

			expect(component.dataLoading).toBe(false);
			expect(component.isEdit).toBe(false);
		});

		it('should handle saveRule network error', () => {
			component.ruleForm.patchValue({
				requestType: 'type1',
				request: ['req1'],
				action: 'allow',
			});
			ruleService.saveRule.and.returnValue(throwError(() => ({ status: 404, message: 'Not found' })));

			component.saveRule();

			expect(component.dataLoading).toBe(false);
			expect(dialogRef.close).not.toHaveBeenCalled();
		});

		it('should handle saveRule timeout error', () => {
			component.ruleForm.patchValue({
				requestType: 'type1',
				request: ['req1'],
				action: 'allow',
			});
			ruleService.saveRule.and.returnValue(throwError(() => ({ status: 408, message: 'Timeout' })));

			component.saveRule();

			expect(component.dataLoading).toBe(false);
			expect(dialogRef.close).not.toHaveBeenCalled();
		});
	});

	describe('Component Properties', () => {
		it('should initialize with correct default values', () => {
			expect(component.isEdit).toBe(false);
			expect(component.isSave).toBe(false);
			expect(component.dataLoading).toBe(false);
			expect(component.requestList).toEqual(orgs);
		});

		it('should have correct requestTypes', () => {
			expect(component.requestTypes).toEqual([
				{ code: '1', name: 'Change Request' },
				{ code: '2', name: 'Subscription Request' },
				{ code: '3', name: 'Access Delegation Request' },
			]);
		});

		it('should have correct initial holderId after initialization', () => {
			// The component is already initialized in beforeEach, so holderId should be set
			expect(component.holderId).toBe('org456');
		});

		it('should update holderId after ngOnInit', () => {
			// Reset holderId to test the initialization
			component.holderId = '';
			component.ngOnInit();
			expect(component.holderId).toBe('org456');
		});
	});

	describe('Component Integration', () => {
		it('should extend RolesAwareComponent', () => {
			expect(component).toBeInstanceOf(RuleDetailComponent);
		});

		it('should have all required methods', () => {
			expect(typeof component.ngOnInit).toBe('function');
			expect(typeof component.getFormData).toBe('function');
			expect(typeof component.saveRule).toBe('function');
		});

		it('should properly initialize with all dependencies', () => {
			expect(component['ruleService']).toBeDefined();
			expect(component['orgService']).toBeDefined();
			expect(component['dialog']).toBeDefined();
			expect(component['translateService']).toBeDefined();
			expect(component['dialogRef']).toBeDefined();
			expect(component.data).toBeDefined();
		});

		it('should handle complete workflow for creating new rule', () => {
			component.data = {
				holder: '',
				requestType: '',
				request: '',
				action: '',
				holderNames: '',
				requestTypeDescription: '',
				requestNames: '',
				actionDescription: '',
			};

			// Initialize component
			component.ngOnInit();

			// Fill form
			component.ruleForm.patchValue({
				requestType: '1',
				request: ['org1'],
				action: 'allow',
			});

			// Save rule
			ruleService.saveRule.and.returnValue(of(''));
			component.saveRule();

			// Verify workflow
			expect(component.holderId).toBe('org456');
			expect(component.isEdit).toBe(false);
			expect(ruleService.saveRule).toHaveBeenCalledWith(
				jasmine.objectContaining({
					holder: ['org456'],
					requestType: '1',
					request: ['org1'],
					action: 'allow',
				})
			);
			expect(dialogRef.close).toHaveBeenCalledWith(true);
		});

		it('should handle complete workflow for editing existing rule', () => {
			const ruleData = { ...mockRule, requestTypeDescription: 'Description' };
			ruleService.getRule.and.returnValue(of(ruleData));
			component.data = mockRuleListObj;

			// Initialize component
			component.ngOnInit();

			// Verify edit mode
			expect(component.isEdit).toBe(true);
			expect(component.ruleForm.value.requestType).toBe('type1');

			// Update form
			component.ruleForm.patchValue({
				action: 'deny',
			});

			// Save rule
			ruleService.saveRule.and.returnValue(of(''));
			component.saveRule();

			// Verify workflow
			expect(ruleService.saveRule).toHaveBeenCalledWith(
				jasmine.objectContaining({
					id: '111',
					action: 'deny',
				})
			);
			expect(dialogRef.close).toHaveBeenCalledWith(true);
		});
	});
});
