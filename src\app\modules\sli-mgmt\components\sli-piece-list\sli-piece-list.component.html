<div class="orll-sli-piece-list">
	@if (source === sourceType.SLI_LIST_CREATE) {
		<div class="row">
			{{ 'sli.mgmt.pieceList.create.step1' | translate }}
		</div>
	}
	<div class="row margin-b-20">
		<div class="col-12">
			<orll-sli-piece-table
				[records]="pieceList"
				[totalRecords]="totalRecords"
				[totalQuantity]="totalQuantity"
				[totalSlac]="totalSlac"
				[pageParams]="pageParams"
				[sliNumber]="sliNumber"
				[sliTemplateNum]="sliTemplateNum"
				[isForHawb]="isForHawb"
				[disableUpdate]="disableUpdate"
				(saveRequest)="createOrUpdatePiece($event)"
				(refresh)="refreshData()"
				(sortChange)="onSortChange($event)"
				(pagination)="onPageChange($event)">
			</orll-sli-piece-table>
		</div>
		@if (source === sourceType.SLI_LIST_CREATE) {
			<div class="col-12 d-flex justify-content-end margin-t-20">
				<button mat-stroked-button color="primary" (click)="next()" [disabled]="pieceList.length === 0">
					<mat-icon>check</mat-icon>
					{{ 'common.dialog.next' | translate }}
				</button>
			</div>
		}
	</div>
</div>
@if (dataLoading) {
	<iata-spinner></iata-spinner>
}
