<div class="orll-rule-detail">
	<h2 mat-dialog-title>
		@if (isEdit) {
			<span>{{ 'system.rule.edit.title' | translate }}</span>
		} @else {
			<span>{{ 'system.rule.create.title' | translate }}</span>
		}

		<mat-icon class="orll-rule-detail__clear_dialog" [matDialogClose]="'cancel'">clear_round</mat-icon>
	</h2>
	<mat-dialog-content>
		<div class="container mt-4">
			<form [formGroup]="ruleForm">
				<div class="row">
					<mat-form-field class="col-12">
						<mat-label>{{ 'system.rule.table.request.type' | translate }}</mat-label>

						<mat-select class="width-100" formControlName="requestType">
							@for (item of requestTypes; track item.code) {
								<mat-option [value]="item.code">
									{{ item.name }}
								</mat-option>
							}
						</mat-select>

						@if (ruleForm.get('requestType')?.hasError('required')) {
							<mat-error>{{
								'validators.required' | translate: { field: 'system.rule.table.request.type' | translate }
							}}</mat-error>
						}
					</mat-form-field>
				</div>

				<div class="row">
					<mat-form-field class="col-12">
						<mat-label>{{ 'system.rule.table.requester' | translate }}</mat-label>
						<mat-select class="width-100" formControlName="request" multiple>
							@for (item of requestList; track item.id) {
								<mat-option [value]="item.id">
									{{ item.name }}
								</mat-option>
							}
						</mat-select>

						@if (ruleForm?.hasError('requestEmpty')) {
							<ng-container>
								<div class="orll-rule-detail__error">{{ ruleForm?.hasError('requestEmpty') }}</div>
								<mat-error>{{ 'system.rule.requester.error1' | translate }}</mat-error>
							</ng-container>
						}
						@if (ruleForm?.hasError('requestIncludeHolder')) {
							<ng-container>
								<div class="orll-rule-detail__error">{{ ruleForm?.hasError('requestIncludeHolder') }}</div>
								<mat-error
									>{{ ruleForm?.hasError('requestIncludeHolder')
									}}{{ 'system.rule.requester.error2' | translate }}</mat-error
								>
							</ng-container>
						}
					</mat-form-field>
				</div>
				<div class="row">
					<div class="col-3">
						<span>{{ 'system.rule.table.action' | translate }}</span>
					</div>
					<div class="col-7">
						<mat-radio-group formControlName="action">
							<mat-radio-button value="1">{{ 'system.rule.approve' | translate }}</mat-radio-button>
							<mat-radio-button value="2">{{ 'system.rule.reject' | translate }}</mat-radio-button>
						</mat-radio-group>

						@if (isSave && ruleForm.get('action')?.hasError('required')) {
							<mat-error>{{
								'validators.required' | translate: { field: 'system.rule.table.action' | translate }
							}}</mat-error>
						}
					</div>
				</div>
			</form>
		</div>
		<mat-divider class="row"></mat-divider>
	</mat-dialog-content>
	<div class="orll-rule-detail__btn d-flex">
		<button mat-stroked-button [mat-dialog-close]="'cancel'" color="primary" class="orll-rule-detail__cancel_btn">
			{{ 'common.dialog.cancel' | translate }}
		</button>
		<button mat-flat-button color="primary" (click)="saveRule()">
			<mat-icon>check</mat-icon>
			{{ 'common.dialog.ok' | translate }}
		</button>
	</div>
</div>
@if (dataLoading) {
	<iata-spinner></iata-spinner>
}
