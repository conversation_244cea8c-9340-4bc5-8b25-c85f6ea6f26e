<!-- Desktop Header -->
<header class="iata-main-header">
	<div class="iata-page-content-container iata-main-header__content">
		<div class="iata-main-header__logo-container">
			<a routerLink="/" class="iata-main-header__logo-link">
				<img src="assets/images/iata-logo.svg" class="iata-main-header__logo" alt="IATA logo" />
				<div>
					<span class="iata-main-header__title">{{ 'common.mainHeader.app.title' | translate }}</span>
					<span class="iata-main-header__subtitle">{{ 'common.mainHeader.app.subtitle' | translate }}</span>
				</div>
			</a>
		</div>

		<div class="iata-main-header__nav-container">
			<nav class="iata-main-header__primary-nav" aria-label="Primary navigation">
				<ul class="iata-main-header__primary-nav__list">
					<li class="iata-main-header__primary-nav__list__item">
						<a class="iata-main-header__primary-nav__list__item__link" routerLink="dashboard">
							<mat-icon class="home" color="primary">home</mat-icon>
						</a>
					</li>

					@if ((isSuperUser() | async) === false) {
						<li class="iata-main-header__primary-nav__list__item">
							<button mat-button [matMenuTriggerFor]="menuShipment" id="shipment" class="primary-menu">
								{{ 'common.mainHeader.mainNav.shipment' | translate }}
							</button>
							<mat-menu #menuShipment="matMenu">
								@if (hasSomeRole(menuSliRoles) | async) {
									<button
										mat-menu-item
										class="iata-main-header__secondary-nav__list__item__link"
										routerLink="sli"
										routerLinkActive="iata-active-nav-item">
										<span class="iata-main-header__secondary-nav__list__item__link__label">{{
											'common.mainHeader.mainNav.sli' | translate
										}}</span>
									</button>
								}
								@if (hasSomeRole(menuHawbRoles) | async) {
									<button
										mat-menu-item
										class="iata-main-header__secondary-nav__list__item__link"
										routerLink="hawb"
										routerLinkActive="iata-active-nav-item">
										<span class="iata-main-header__secondary-nav__list__item__link__label">{{
											'common.mainHeader.mainNav.hawb' | translate
										}}</span>
									</button>
								}
								@if (hasSomeRole(menuMawbRoles) | async) {
									<button
										mat-menu-item
										class="iata-main-header__secondary-nav__list__item__link"
										routerLink="mawb"
										routerLinkActive="iata-active-nav-item">
										<span class="iata-main-header__secondary-nav__list__item__link__label">{{
											'common.mainHeader.mainNav.mawb' | translate
										}}</span>
									</button>
								}
								@if (hasSomeRole(menuChangeRequestRoles) | async) {
									<button
										mat-menu-item
										class="iata-main-header__secondary-nav__list__item__link"
										routerLink="change-requests"
										routerLinkActive="iata-active-nav-item">
										<span class="iata-main-header__secondary-nav__list__item__link__label">{{
											'common.mainHeader.mainNav.changeRequests' | translate
										}}</span>
									</button>
								}
							</mat-menu>
						</li>
					}

					@if ((isSuperUser() | async) === false) {
						<li class="iata-main-header__primary-nav__list__item">
							<button mat-button [matMenuTriggerFor]="menuPartner" id="partner" class="primary-menu">
								{{ 'common.mainHeader.mainNav.partner' | translate }}
							</button>
							<mat-menu #menuPartner="matMenu">
								<button
									mat-menu-item
									class="iata-main-header__secondary-nav__list__item__link"
									routerLink="subscription"
									routerLinkActive="iata-active-nav-item">
									<span class="iata-main-header__secondary-nav__list__item__link__label">{{
										'common.mainHeader.mainNav.partner.subscription' | translate
									}}</span>
								</button>
								<button
									mat-menu-item
									class="iata-main-header__secondary-nav__list__item__link"
									routerLink="partner"
									routerLinkActive="iata-active-nav-item">
									<span class="iata-main-header__secondary-nav__list__item__link__label">{{
										'common.mainHeader.mainNav.partner.permission' | translate
									}}</span>
								</button>
								<button
									mat-menu-item
									class="iata-main-header__secondary-nav__list__item__link"
									routerLink="rule-mgmt"
									routerLinkActive="iata-active-nav-item">
									<span class="iata-main-header__secondary-nav__list__item__link__label">{{
										'system.rule.title' | translate
									}}</span>
								</button>
							</mat-menu>
						</li>
					}

					@if (isSuperUser() | async) {
						<li class="iata-main-header__primary-nav__list__item">
							<button mat-button [matMenuTriggerFor]="menuSystem" id="system" class="primary-menu">
								{{ 'common.mainHeader.mainNav.system' | translate }}
							</button>
							<mat-menu #menuSystem="matMenu">
								<button
									mat-menu-item
									class="iata-main-header__secondary-nav__list__item__link"
									routerLink="users-mgmt"
									routerLinkActive="iata-active-nav-item">
									<span class="iata-main-header__secondary-nav__list__item__link__label">{{
										'common.mainHeader.mainNav.users' | translate
									}}</span>
								</button>
								<button
									mat-menu-item
									class="iata-main-header__secondary-nav__list__item__link"
									routerLink="server-mgmt"
									routerLinkActive="iata-active-nav-item">
									<span class="iata-main-header__secondary-nav__list__item__link__label">Server</span>
								</button>
							</mat-menu>
						</li>
					}

					@if (hasSomeRole(menuDistributionRoles) | async) {
						<li class="iata-main-header__primary-nav__list__item">
							<button mat-button [matMenuTriggerFor]="menuDistribution" id="distribution" class="primary-menu">
								{{ 'common.mainHeader.mainNav.distribution' | translate }}
							</button>
							<mat-menu #menuDistribution="matMenu">
								<button
									mat-menu-item
									class="iata-main-header__secondary-nav__list__item__link"
									routerLink="quote"
									routerLinkActive="iata-active-nav-item">
									<span class="iata-main-header__secondary-nav__list__item__link__label">{{
										'common.mainHeader.mainNav.distribution.quote' | translate
									}}</span>
								</button>
								<button
									mat-menu-item
									class="iata-main-header__secondary-nav__list__item__link"
									routerLink="booking"
									routerLinkActive="iata-active-nav-item">
									<span class="iata-main-header__secondary-nav__list__item__link__label">{{
										'common.mainHeader.mainNav.distribution.booking' | translate
									}}</span>
								</button>
							</mat-menu>
						</li>
					}
				</ul>
			</nav>
			<ng-container>
				<nav class="iata-main-header__secondary-nav iata-main-header__side-nav" aria-label="Secondary navigation">
					<ul class="iata-main-header__secondary-nav__list">
						<li class="iata-main-header__secondary-nav__list__item">
							<a class="iata-main-header__secondary-nav__list__item__link">
								<mat-icon
									[matBadge]="notificationNum"
									[matBadgeHidden]="notificationOpen || notificationNum <= 0"
									fontSet="material-icons-outlined"
									matBadgeColor="warn"
									color="primary"
									(click)="$event.stopPropagation(); notificationOpen = !notificationOpen"
									(keydown.enter)="$event.stopPropagation(); notificationOpen = !notificationOpen"
									cdkOverlayOrigin
									#notificationPop="cdkOverlayOrigin">
									@if (!notificationOpen) {
										notifications
									} @else {
										cancel_presentation
									}
								</mat-icon>
							</a>
						</li>
					</ul>
				</nav>
				<ng-template
					cdkConnectedOverlay
					[cdkConnectedOverlayOrigin]="notificationPop"
					[cdkConnectedOverlayOpen]="notificationOpen"
					(detach)="notificationOpen = false">
					<orll-notification-popup
						[refreshNotification]="notificationOpen"
						(showOpen)="toggleNotificationPanel($event)"></orll-notification-popup>
				</ng-template>
			</ng-container>

			<ng-container>
				<nav aria-label="Secondary navigation">
					<ul class="iata-main-header__secondary-nav__list lang-ul">
						<div class="row">
							<a class="lang-link" (click)="switchLanguage('en')">
								<span class="{{ currentLang === 'en' ? 'lang-name' : 'role-name' }}">
									{{ 'common.mainHeader.language.english' | translate }}
								</span>
							</a>
							<span>/</span>
							<a class="lang-link" (click)="switchLanguage('zh')">
								<span class="{{ currentLang === 'zh' ? 'lang-name' : 'role-name' }}">
									{{ 'common.mainHeader.language.chinese' | translate }}
								</span>
							</a>
						</div>
					</ul>
				</nav>
			</ng-container>

			<ng-container *ngIf="getCurrentUser() | async as user">
				<nav class="iata-main-header__secondary-nav" aria-label="Secondary navigation">
					<ul class="iata-main-header__secondary-nav__list">
						<li class="iata-main-header__secondary-nav__list__item">
							<a class="iata-main-header__secondary-nav__list__item__link">
								<mat-icon
									color="primary"
									class="user-toggle"
									aria-hidden="false"
									aria-label="{{ 'common.mainHeader.account' | translate }}"
									matTooltip="{{ 'common.mainHeader.account' | translate }}"
									(click)="$event.stopPropagation(); isOpen = !isOpen"
									(keydown.enter)="$event.stopPropagation(); isOpen = !isOpen"
									cdkOverlayOrigin
									#trigger="cdkOverlayOrigin"
									>account_circle</mat-icon
								>
							</a>
							<div class="username">
								<span>{{ user?.firstName }} {{ user?.lastName }}</span>
								<span>{{ user?.userType === 3 ? '' : ', ' + user?.primaryOrgName }}</span>
							</div>
						</li>
					</ul>
				</nav>

				<ng-template
					cdkConnectedOverlay
					[cdkConnectedOverlayOrigin]="trigger"
					[cdkConnectedOverlayOpen]="isOpen"
					(backdropClick)="isOpen = false"
					[cdkConnectedOverlayHasBackdrop]="true">
					<ul class="iata-main-header__secondary-nav__dropdown-list">
						<div class="role-title">{{ user?.firstName }} {{ user?.lastName }}</div>
						<mat-divider class="divider"></mat-divider>
						<li class="iata-main-header__secondary-nav__dropdown-list__item">
							<div class="current-role">
								<mat-icon color="primary" aria-hidden="false" aria-label="{{ 'common.mainHeader.userid' | translate }}"
									>person</mat-icon
								>
								<span>{{ user?.primaryOrgName }}</span>
							</div>
						</li>
						@for (role of user?.orgList; track role.id) {
							<li class="iata-main-header__secondary-nav__dropdown-list__item">
								<a
									class="iata-main-header__secondary-nav__dropdown-list__item__link"
									(click)="$event.stopPropagation(); onSwitch(role)"
									(keydown.enter)="$event.stopPropagation(); onSwitch(role)">
									<span class="role-name">{{ role.name }}</span>
								</a>
							</li>
						}
					</ul>
				</ng-template>
			</ng-container>
		</div>
	</div>
</header>
